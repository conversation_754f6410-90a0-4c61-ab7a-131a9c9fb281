<?php

namespace App\Http\Controllers;

use App\Services\CaptchaService;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Captcha",
 *     description="验证码接口"
 * )
 */
class CaptchaController extends Controller
{
    protected $captchaService;

    public function __construct(CaptchaService $captchaService)
    {
        $this->captchaService = $captchaService;
    }

    /**
     * @OA\Get(
     *     path="/api/captcha",
     *     summary="获取验证码",
     *     tags={"Captcha"},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="key", type="string", description="验证码key"),
     *             @OA\Property(property="image", type="string", description="验证码图片（base64）")
     *         )
     *     )
     * )
     */
    public function generate()
    {
        $captcha = $this->captchaService->generateCaptcha();
        return response()->json($captcha);
    }

    /**
     * @OA\Post(
     *     path="/api/captcha/verify",
     *     summary="验证验证码",
     *     tags={"Captcha"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="key", type="string"),
     *             @OA\Property(property="code", type="string")
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功"),
     *     @OA\Response(response=400, description="验证码错误")
     * )
     */
    public function verify(Request $request)
    {
        $key = $request->input('key');
        $code = $request->input('code');

        if ($this->captchaService->validateCaptcha($key, $code)) {
            return response()->json(['message' => '验证成功']);
        }

        return response()->json(['message' => '验证码错误'], 400);
    }
}
