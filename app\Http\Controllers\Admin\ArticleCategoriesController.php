<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ArticleCategory;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Admin-ArticleCategories",
 *     description="超级管理员文章分类管理"
 * )
 */
class ArticleCategoriesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/article-categories",
     *     summary="获取文章分类列表",
     *     description="获取文章分类列表，支持分页",
     *     tags={"Admin-ArticleCategories"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer", description="当前页码"),
     *             @OA\Property(property="data", type="array", description="分类列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="分类ID"),
     *                     @OA\Property(property="name", type="string", description="分类名称"),
     *                     @OA\Property(property="created_at", type="string", description="创建时间"),
     *                     @OA\Property(property="updated_at", type="string", description="更新时间")
     *                 )
     *             ),
     *             @OA\Property(property="total", type="integer", description="总数量")
     *         )
     *     )
     * )
     */
    public function index()
    {
        $categories = ArticleCategory::orderBy('created_at', 'desc')->paginate(20);
        return response()->json($categories);
    }

    /**
     * @OA\Post(
     *     path="/admin/article-categories",
     *     summary="创建文章分类",
     *     description="创建新的文章分类",
     *     tags={"Admin-ArticleCategories"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"name"},
     *                 @OA\Property(property="name", type="string", description="分类名称")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="创建成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="创建成功"),
     *             @OA\Property(property="category", type="object", description="分类信息")
     *         )
     *     ),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:article_categories,name'
        ], [
            'name.required' => '分类名称不能为空',
            'name.max' => '分类名称不能超过255个字符',
            'name.unique' => '分类名称已存在'
        ]);

        $category = ArticleCategory::create([
            'name' => $request->input('name')
        ]);

        return response()->json([
            'message' => '创建成功',
            'category' => $category
        ]);
    }

    /**
     * @OA\Get(
     *     path="/admin/article-categories/{id}",
     *     summary="获取文章分类详情",
     *     description="获取指定文章分类的详细信息",
     *     tags={"Admin-ArticleCategories"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="分类ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="分类ID"),
     *             @OA\Property(property="name", type="string", description="分类名称"),
     *             @OA\Property(property="created_at", type="string", description="创建时间"),
     *             @OA\Property(property="updated_at", type="string", description="更新时间")
     *         )
     *     ),
     *     @OA\Response(response=404, description="分类不存在")
     * )
     */
    public function show($id)
    {
        $category = ArticleCategory::findOrFail($id);
        return response()->json($category);
    }

    /**
     * @OA\Put(
     *     path="/admin/article-categories/{id}",
     *     summary="更新文章分类",
     *     description="更新文章分类信息",
     *     tags={"Admin-ArticleCategories"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="分类ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"name"},
     *                 @OA\Property(property="name", type="string", description="分类名称")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="更新成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="更新成功"),
     *             @OA\Property(property="category", type="object", description="分类信息")
     *         )
     *     ),
     *     @OA\Response(response=404, description="分类不存在"),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function update(Request $request, $id)
    {
        $category = ArticleCategory::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255|unique:article_categories,name,' . $id
        ], [
            'name.required' => '分类名称不能为空',
            'name.max' => '分类名称不能超过255个字符',
            'name.unique' => '分类名称已存在'
        ]);

        $category->update([
            'name' => $request->input('name')
        ]);

        return response()->json([
            'message' => '更新成功',
            'category' => $category->fresh()
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/admin/article-categories/{id}",
     *     summary="删除文章分类",
     *     description="软删除文章分类",
     *     tags={"Admin-ArticleCategories"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="分类ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="删除成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="删除成功")
     *         )
     *     ),
     *     @OA\Response(response=404, description="分类不存在")
     * )
     */
    public function destroy($id)
    {
        $category = ArticleCategory::findOrFail($id);

        // 检查是否有文章使用此分类
        $articleCount = $category->articles()->count();
        if ($articleCount > 0) {
            return response()->json([
                'message' => "该分类下还有 {$articleCount} 篇文章，无法删除"
            ], 400);
        }

        $category->delete(); // 软删除

        return response()->json(['message' => '删除成功']);
    }
}
