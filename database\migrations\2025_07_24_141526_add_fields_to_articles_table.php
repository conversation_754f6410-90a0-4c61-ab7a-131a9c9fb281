<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->string('author')->nullable()->comment('作者');
            $table->text('summary')->nullable()->comment('摘要');
            $table->unsignedInteger('views')->default(0)->comment('阅读量');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->dropColumn(['author', 'summary', 'views', 'published_at']);
        });
    }
};
