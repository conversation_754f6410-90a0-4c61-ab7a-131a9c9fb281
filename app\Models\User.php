<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use App\Models\Traits\SerializeDate;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SerializeDate;

    protected $fillable = ['phone', 'password', 'role', 'status'];

    protected $casts = [
        'role' => 'string',
        'status' => 'string',
    ];

    public function agency()
    {
        return $this->hasOne(Agency::class);
    }

    public function broker()
    {
        return $this->hasOne(Broker::class);
    }
}
