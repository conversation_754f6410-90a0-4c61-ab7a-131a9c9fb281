<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TrainingPlan;
use App\Models\TrainingPlanCourse;
use App\Models\Broker;
use App\Models\Order;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Admin-TrainingPlans",
 *     description="培训计划管理"
 * )
 */
class TrainingPlansController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/training-plans",
     *     summary="培训计划列表",
     *     tags={"Admin-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index()
    {
        $plans = TrainingPlan::paginate(20);
        return response()->json($plans);
    }

    /**
     * @OA\Post(
     *     path="/admin/training-plans",
     *     summary="创建培训计划",
     *     tags={"Admin-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="deadline", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function store(Request $request)
    {
        $plan = TrainingPlan::create($request->all());
        return response()->json(['message' => '创建成功', 'plan' => $plan]);
    }

    /**
     * @OA\Get(
     *     path="/admin/training-plans/{id}",
     *     summary="培训计划详情",
     *     tags={"Admin-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="培训计划ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show($id)
    {
        $plan = TrainingPlan::findOrFail($id);
        return response()->json($plan);
    }

    /**
     * @OA\Put(
     *     path="/admin/training-plans/{id}",
     *     summary="编辑培训计划",
     *     tags={"Admin-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="培训计划ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="deadline", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function update(Request $request, $id)
    {
        $plan = TrainingPlan::findOrFail($id);
        $plan->update($request->all());
        return response()->json(['message' => '更新成功']);
    }

    /**
     * @OA\Delete(
     *     path="/admin/training-plans/{id}",
     *     summary="删除培训计划",
     *     tags={"Admin-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="培训计划ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function destroy($id)
    {
        $plan = TrainingPlan::findOrFail($id);
        $plan->delete();
        return response()->json(['message' => '删除成功']);
    }

    public function assign(Request $request, $id)
    {
        $plan = TrainingPlan::findOrFail($id);
        $brokers = Broker::where('status', 'approved')->get();
        foreach ($brokers as $broker) {
            Order::create([
                'broker_id' => $broker->id,
                'training_plan_id' => $plan->id,
                'amount' => $plan->price,
                'status' => 'pending'
            ]);
        }
        return response()->json(['message' => '培训计划已下发给所有审核通过的经纪人']);
    }

    public function courses($id)
    {
        $plan = TrainingPlan::findOrFail($id);
        $courses = $plan->courses;
        return response()->json($courses);
    }
}
