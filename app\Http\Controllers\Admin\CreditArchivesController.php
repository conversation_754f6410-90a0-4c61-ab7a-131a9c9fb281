<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CreditArchive;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Admin-CreditArchives",
 *     description="信用档案管理"
 * )
 */
class CreditArchivesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/credit-archives",
     *     summary="信用档案列表",
     *     tags={"Admin-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="entity_type",
     *         in="query",
     *         description="主体类型筛选",
     *         @OA\Schema(type="string", enum={"agency","broker"})
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="档案类型筛选",
     *         @OA\Schema(type="string", enum={"red","black"})
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="每页数量",
     *         @OA\Schema(type="integer", minimum=1, maximum=100, default=20)
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         @OA\Schema(type="integer", minimum=1, default=1)
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $query = CreditArchive::query();

        // 按主体类型筛选
        if ($request->has('entity_type') && in_array($request->entity_type, ['agency', 'broker'])) {
            $query->where('entity_type', $request->entity_type);
        }

        // 按档案类型筛选
        if ($request->has('type') && in_array($request->type, ['red', 'black'])) {
            $query->where('type', $request->type);
        }

        // 处理分页参数
        $perPage = $request->get('per_page', 20);
        $perPage = max(1, min(100, (int)$perPage)); // 限制在1-100之间

        $archives = $query->paginate($perPage);
        return response()->json($archives);
    }

    /**
     * @OA\Post(
     *     path="/admin/credit-archives",
     *     summary="创建信用档案",
     *     tags={"Admin-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="entity_type", type="string", enum={"agency","broker"}),
     *                 @OA\Property(property="entity_id", type="integer"),
     *                 @OA\Property(property="type", type="string", enum={"red","black"}),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="content", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function store(Request $request)
    {
        $archive = CreditArchive::create($request->all());
        return response()->json(['message' => '创建成功', 'archive' => $archive]);
    }

    /**
     * @OA\Get(
     *     path="/admin/credit-archives/{id}",
     *     summary="信用档案详情",
     *     tags={"Admin-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="档案ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show($id)
    {
        $archive = CreditArchive::findOrFail($id);
        return response()->json($archive);
    }

    /**
     * @OA\Put(
     *     path="/admin/credit-archives/{id}",
     *     summary="编辑信用档案",
     *     tags={"Admin-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="档案ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="entity_type", type="string", enum={"agency","broker"}),
     *                 @OA\Property(property="entity_id", type="integer"),
     *                 @OA\Property(property="type", type="string", enum={"red","black"}),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="content", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function update(Request $request, $id)
    {
        $archive = CreditArchive::findOrFail($id);
        $archive->update($request->all());
        return response()->json(['message' => '更新成功']);
    }

    /**
     * @OA\Delete(
     *     path="/admin/credit-archives/{id}",
     *     summary="删除信用档案",
     *     tags={"Admin-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="档案ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function destroy($id)
    {
        $archive = CreditArchive::findOrFail($id);
        $archive->delete();
        return response()->json(['message' => '删除成功']);
    }
}
