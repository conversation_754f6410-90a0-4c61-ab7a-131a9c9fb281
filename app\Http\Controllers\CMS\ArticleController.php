<?php

namespace App\Http\Controllers\CMS;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\ArticleCategory;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="CMS-Articles",
 *     description="CMS端文章管理"
 * )
 */
class ArticleController extends Controller
{
    /**
     * @OA\Get(
     *     path="/cms/categories",
     *     summary="获取文章分类列表",
     *     description="获取所有文章分类列表，无需登录权限",
     *     tags={"CMS-Articles"},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="integer", description="分类ID"),
     *                 @OA\Property(property="name", type="string", description="分类名称"),
     *                 @OA\Property(property="created_at", type="string", description="创建时间"),
     *                 @OA\Property(property="updated_at", type="string", description="更新时间")
     *             )
     *         )
     *     )
     * )
     */
    public function categories()
    {
        $categories = ArticleCategory::select(['id', 'name', 'created_at', 'updated_at'])
            ->orderBy('name')
            ->get();
        return response()->json($categories);
    }

    /**
     * @OA\Get(
     *     path="/cms/categories/{id}",
     *     summary="获取文章分类详情",
     *     description="根据ID获取单个文章分类的详细信息，无需登录权限",
     *     tags={"CMS-Articles"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="分类ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="分类ID"),
     *             @OA\Property(property="name", type="string", description="分类名称"),
     *             @OA\Property(property="created_at", type="string", description="创建时间"),
     *             @OA\Property(property="updated_at", type="string", description="更新时间")
     *         )
     *     ),
     *     @OA\Response(response=404, description="分类不存在")
     * )
     */
    public function categoryDetail($id)
    {
        $category = ArticleCategory::select(['id', 'name', 'created_at', 'updated_at'])
            ->findOrFail($id);
        return response()->json($category);
    }

    /**
     * @OA\Get(
     *     path="/cms/articles",
     *     summary="获取文章列表",
     *     description="获取已发布的文章列表，支持分页和分类筛选",
     *     tags={"CMS-Articles"},
     *     @OA\Parameter(
     *         name="category_id",
     *         in="query",
     *         required=false,
     *         description="分类ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         required=false,
     *         description="页码",
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="每页数量",
     *         @OA\Schema(type="integer", default=20, minimum=1, maximum=100)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer", description="当前页码"),
     *             @OA\Property(property="data", type="array", description="文章列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="文章ID"),
     *                     @OA\Property(property="title", type="string", description="标题"),
     *                     @OA\Property(property="author", type="string", description="作者"),
     *                     @OA\Property(property="summary", type="string", description="摘要"),
     *                     @OA\Property(property="views", type="integer", description="阅读量"),
     *                     @OA\Property(property="published_at", type="string", description="发布时间"),
     *                     @OA\Property(property="category", type="object", description="分类信息")
     *                 )
     *             ),
     *             @OA\Property(property="total", type="integer", description="总数量")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        // 验证分页参数
        $perPage = $request->input('per_page', 20);
        if ($perPage < 1 || $perPage > 100) {
            $perPage = 20;
        }

        $query = Article::with('category')
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now());

        if ($request->has('category_id') && $request->input('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        $articles = $query->orderBy('published_at', 'desc')->paginate($perPage);
        return response()->json($articles);
    }

    /**
     * @OA\Get(
     *     path="/cms/articles/{id}",
     *     summary="获取文章详情",
     *     description="获取文章详细信息，每次访问阅读量+1",
     *     tags={"CMS-Articles"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="文章ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="文章ID"),
     *             @OA\Property(property="title", type="string", description="标题"),
     *             @OA\Property(property="content", type="string", description="文章详情"),
     *             @OA\Property(property="author", type="string", description="作者"),
     *             @OA\Property(property="summary", type="string", description="摘要"),
     *             @OA\Property(property="views", type="integer", description="阅读量"),
     *             @OA\Property(property="published_at", type="string", description="发布时间"),
     *             @OA\Property(property="category", type="object", description="分类信息")
     *         )
     *     ),
     *     @OA\Response(response=404, description="文章不存在")
     * )
     */
    public function show($id)
    {
        $article = Article::with('category')->findOrFail($id);

        // 检查文章是否已发布
        if (!$article->published_at || $article->published_at > now()) {
            return response()->json(['message' => '文章不存在或未发布'], 404);
        }

        // 增加阅读量
        $article->increment('views');

        return response()->json($article);
    }

    /**
     * @OA\Get(
     *     path="/cms/categories-with-articles",
     *     summary="获取分类及其文章列表",
     *     description="获取所有分类及每个分类下的前N篇文章",
     *     tags={"CMS-Articles"},
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="每个分类返回的文章数量",
     *         required=false,
     *         @OA\Schema(type="integer", default=10, minimum=1, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array", description="分类列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="分类ID"),
     *                     @OA\Property(property="name", type="string", description="分类名称"),
     *                     @OA\Property(property="articles", type="array", description="文章列表",
     *                         @OA\Items(
     *                             @OA\Property(property="id", type="integer", description="文章ID"),
     *                             @OA\Property(property="title", type="string", description="文章标题"),
     *                             @OA\Property(property="created_at", type="string", description="创建时间")
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function categoriesWithArticles(Request $request)
    {
        // 验证参数
        $perPage = $request->input('per_page', 10);
        if ($perPage < 1 || $perPage > 50) {
            $perPage = 10;
        }

        // 获取所有分类及其文章
        $categories = ArticleCategory::with([
            'articles' => function ($query) use ($perPage) {
                $query->select(['id', 'category_id', 'title', 'created_at'])
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now())
                    ->orderBy('published_at', 'desc')
                    ->limit($perPage);
            }
        ])
        ->select(['id', 'name'])
        ->orderBy('name')
        ->get();

        // 格式化响应数据
        $data = $categories->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'articles' => $category->articles->map(function ($article) {
                    return [
                        'id' => $article->id,
                        'title' => $article->title,
                        'created_at' => $article->created_at->format('Y-m-d H:i:s')
                    ];
                })
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    public function myArticles(Request $request)
    {
        $user = $request->user();
        if ($user->role === 'broker') {
            $broker = $user->broker;
            if (!$broker) {
                return response()->json(['message' => '经纪人信息不存在'], 404);
            }
            // 可以根据需求返回与经纪人相关的文章
            $articles = Article::where('category_id', 1)->paginate(20); // 假设category_id=1是经纪人相关分类
            return response()->json($articles);
        } elseif ($user->role === 'agency_admin') {
            $agency = $user->agency;
            if (!$agency) {
                return response()->json(['message' => '企业信息不存在'], 404);
            }
            // 可以根据需求返回与企业相关的文章
            $articles = Article::where('category_id', 2)->paginate(20); // 假设category_id=2是企业相关分类
            return response()->json($articles);
        }
        return response()->json(['message' => '无权访问'], 403);
    }
}
