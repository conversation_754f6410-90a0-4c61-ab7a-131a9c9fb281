<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use Illuminate\Http\Request;

/**
 * @OA\Info(
 *     version="1.0.0",
 *     title="DFH管理系统 API 文档",
 *     description="本系统用于测试DEMO等功能。",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     )
 * )
 *
 * @OA\Server(
 *     url="http://127.0.0.1:8000/api",
 *     description="开发环境服务器"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="sanctum",
 *     type="apiKey",
 *     in="header",
 *     name="Authorization",
 *     description="Enter token in format (Bearer <token>)"
 * )
 */
class AgenciesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/agencies",
     *     summary="中介机构列表",
     *     tags={"Admin-Agencies"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="搜索关键词（名称/机构代码/地址/法人姓名）",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="审核状态过滤",
     *         @OA\Schema(type="string", enum={"pending", "approved", "rejected"})
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="每页数量",
     *         @OA\Schema(type="integer", default=20)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(type="object")),
     *             @OA\Property(property="current_page", type="integer"),
     *             @OA\Property(property="last_page", type="integer"),
     *             @OA\Property(property="per_page", type="integer"),
     *             @OA\Property(property="total", type="integer")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = Agency::with('user');

        // 搜索功能
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('org_code', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('legal_name', 'like', "%{$search}%");
            });
        }

        // 状态过滤
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // 分页
        $perPage = $request->input('per_page', 20);
        $agencies = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($agencies);
    }

    /**
     * @OA\Get(
     *     path="/admin/agencies/{id}",
     *     summary="中介机构详情",
     *     tags={"Admin-Agencies"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="机构ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="org_code", type="string"),
     *             @OA\Property(property="org_code_image", type="string"),
     *             @OA\Property(property="address", type="string"),
     *             @OA\Property(property="legal_name", type="string"),
     *             @OA\Property(property="contact", type="string"),
     *             @OA\Property(property="license_number", type="string"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="reject_reason", type="string"),
     *             @OA\Property(property="user", type="object")
     *         )
     *     ),
     *     @OA\Response(response=404, description="机构不存在")
     * )
     */
    public function show($id)
    {
        $agency = Agency::with('user')->findOrFail($id);
        return response()->json($agency);
    }

    /**
     * @OA\Post(
     *     path="/admin/agencies/{id}/verify",
     *     summary="审核中介机构",
     *     tags={"Admin-Agencies"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="机构ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", enum={"approved","rejected"}, description="审核状态"),
     *             @OA\Property(property="reject_reason", type="string", description="拒绝原因（仅当status=rejected时必填）")
     *         )
     *     ),
     *     @OA\Response(response=200, description="审核成功"),
     *     @OA\Response(response=400, description="无效的审核状态或缺少拒绝原因"),
     *     @OA\Response(response=404, description="机构不存在")
     * )
     */
    public function verify(Request $request, $id)
    {
        $agency = Agency::findOrFail($id);
        $status = $request->input('status');

        if (!in_array($status, ['approved', 'rejected'])) {
            return response()->json(['message' => '无效的审核状态'], 400);
        }

        // 如果拒绝，需要提供拒绝原因
        if ($status === 'rejected') {
            if (!$request->has('reject_reason') || empty($request->reject_reason)) {
                return response()->json(['message' => '拒绝时必须提供拒绝原因'], 400);
            }
            $agency->reject_reason = $request->input('reject_reason');
        } else {
            // 如果批准，清除之前的拒绝原因
            $agency->reject_reason = null;
        }

        $agency->status = $status;
        $agency->save();

        return response()->json(['message' => '审核成功']);
    }

    /**
     * @OA\Put(
     *     path="/admin/agencies/{id}/review",
     *     summary="审核中介机构（PUT方法）",
     *     tags={"Admin-Agencies"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="机构ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", enum={"approved","rejected"}, description="审核状态"),
     *             @OA\Property(property="reject_reason", type="string", description="拒绝原因（仅当status=rejected时必填）")
     *         )
     *     ),
     *     @OA\Response(response=200, description="审核成功"),
     *     @OA\Response(response=400, description="无效的审核状态或缺少拒绝原因"),
     *     @OA\Response(response=404, description="机构不存在")
     * )
     */

    /**
     * @OA\Put(
     *     path="/admin/agencies/{id}",
     *     summary="编辑中介机构",
     *     tags={"Admin-Agencies"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="机构ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="org_code", type="string"),
     *             @OA\Property(property="org_code_image", type="string"),
     *             @OA\Property(property="address", type="string"),
     *             @OA\Property(property="legal_name", type="string"),
     *             @OA\Property(property="contact", type="string"),
     *             @OA\Property(property="license_number", type="string"),
     *             @OA\Property(property="status", type="string")
     *         )
     *     ),
     *     @OA\Response(response=200, description="更新成功"),
     *     @OA\Response(response=404, description="机构不存在")
     * )
     */
    public function update(Request $request, $id)
    {
        $agency = Agency::findOrFail($id);
        $agency->update($request->all());
        return response()->json(['message' => '更新成功']);
    }

    /**
     * @OA\Delete(
     *     path="/admin/agencies/{id}",
     *     summary="删除中介机构（软删除）",
     *     tags={"Admin-Agencies"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="机构ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="删除成功"),
     *     @OA\Response(response=404, description="机构不存在")
     * )
     */
    public function destroy($id)
    {
        $agency = Agency::findOrFail($id);
        $agency->delete();
        return response()->json(['message' => '删除成功']);
    }
}
