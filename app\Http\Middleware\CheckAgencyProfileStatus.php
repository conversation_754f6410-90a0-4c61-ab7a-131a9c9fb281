<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckAgencyProfileStatus
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        $agency = $user->agency;

        if (!$agency) {
            return response()->json(['message' => '请填写审核资料，并提交审核。'], 422);
        }

        if ($agency->status === 'pending') {
            return response()->json(['message' => '账户待审核，请等待审核即可。'], 423);
        }

        if ($agency->status === 'rejected') {
            return response()->json([
                'message' => '账户审核被驳回，请根据驳回信息重新修改信息，重新提交审核。',
                'reject_reason' => $agency->reject_reason ?? ''
            ], 403);
        }

        // 审核通过
        return $next($request);
    }
}
