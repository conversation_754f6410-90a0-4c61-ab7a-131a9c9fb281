<?php

namespace App\Services;

use App\Models\Certificate;
use App\Models\Broker;
use App\Models\TrainingPlan;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class CertificateService
{
    public function generateCertificate(Broker $broker, TrainingPlan $trainingPlan)
    {
        $certificateNumber = 'CERT-' . date('Ymd') . '-' . str_pad($broker->id, 5, '0', STR_PAD_LEFT);
        $certificate = Certificate::create([
            'broker_id' => $broker->id,
            'training_plan_id' => $trainingPlan->id,
            'certificate_number' => $certificateNumber,
            'certificate_image' => ''
        ]);

        $templatePath = storage_path('app/public/certificate_template.png');
        if (!file_exists($templatePath)) {
            throw new \Exception('证书模板文件不存在');
        }

        $image = Image::make($templatePath);
        $image->text($certificateNumber, 100, 100, function($font) {
            $font->file(public_path('fonts/simhei.ttf'));
            $font->size(24);
            $font->color('#000000');
            $font->align('left');
            $font->valign('top');
        });
        $image->text($broker->name, 100, 150, function($font) {
            $font->file(public_path('fonts/simhei.ttf'));
            $font->size(24);
            $font->color('#000000');
            $font->align('left');
            $font->valign('top');
        });
        $image->text($trainingPlan->title, 100, 200, function($font) {
            $font->file(public_path('fonts/simhei.ttf'));
            $font->size(24);
            $font->color('#000000');
            $font->align('left');
            $font->valign('top');
        });
        $image->text(date('Y-m-d'), 100, 250, function($font) {
            $font->file(public_path('fonts/simhei.ttf'));
            $font->size(24);
            $font->color('#000000');
            $font->align('left');
            $font->valign('top');
        });

        $path = 'public/certificates/certificate_' . $certificate->id . '.png';
        Storage::put($path, $image->encode('png'));
        $certificate->certificate_image = Storage::url($path);
        $certificate->save();

        return $certificate;
    }
}
