<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Admin-Orders",
 *     description="订单管理"
 * )
 */
class OrdersController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/orders",
     *     summary="订单列表",
     *     tags={"Admin-Orders"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index()
    {
        $orders = Order::paginate(20);
        return response()->json($orders);
    }

    /**
     * @OA\Get(
     *     path="/admin/orders/{id}",
     *     summary="订单详情",
     *     tags={"Admin-Orders"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="订单ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show($id)
    {
        $order = Order::findOrFail($id);
        return response()->json($order);
    }
}
