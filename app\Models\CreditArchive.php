<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CreditArchive extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['entity_type', 'entity_name', 'type', 'title', 'content'];

    protected $casts = [
        'type' => 'string',
    ];

    // 移除 morphTo 关系，改为使用 entity_name 字段手动填写主体名称
}
