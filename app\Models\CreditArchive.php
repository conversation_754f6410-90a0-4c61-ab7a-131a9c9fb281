<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CreditArchive extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['entity_type', 'entity_id', 'type', 'title', 'content'];

    protected $casts = [
        'type' => 'string',
    ];

    public function entity()
    {
        return $this->morphTo();
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class, 'entity_id')->where('entity_type', 'agency');
    }

    public function broker()
    {
        return $this->belongsTo(Broker::class, 'entity_id')->where('entity_type', 'broker');
    }
}
