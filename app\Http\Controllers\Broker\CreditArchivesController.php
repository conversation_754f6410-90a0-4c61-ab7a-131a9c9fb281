<?php

namespace App\Http\Controllers\Broker;

use App\Http\Controllers\Controller;
use App\Models\CreditArchive;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Broker-CreditArchives",
 *     description="经纪人端信用档案查看"
 * )
 */
class CreditArchivesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/broker/credit-archives",
     *     summary="经纪人信用档案列表",
     *     tags={"Broker-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $archives = CreditArchive::where('entity_type', 'broker')
            ->where('entity_id', $broker->id)
            ->paginate(20);
        return response()->json($archives);
    }
}
