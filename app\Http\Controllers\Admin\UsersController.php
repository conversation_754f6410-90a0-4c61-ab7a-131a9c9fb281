<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Admin-Users",
 *     description="账号管理"
 * )
 */
class UsersController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/users",
     *     summary="用户列表",
     *     tags={"Admin-Users"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index()
    {
        $users = User::paginate(20);
        return response()->json($users);
    }

    /**
     * @OA\Post(
     *     path="/admin/users/{id}/reset-password",
     *     summary="重置用户密码",
     *     tags={"Admin-Users"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="用户ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function resetPassword(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $newPassword = Str::random(8);
        $user->password = bcrypt($newPassword);
        $user->save();
        return response()->json(['message' => '密码重置成功', 'new_password' => $newPassword]);
    }

    /**
     * @OA\Post(
     *     path="/admin/users/{id}/toggle-status",
     *     summary="禁用/启用用户账号",
     *     tags={"Admin-Users"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="用户ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="status", type="string", enum={"enabled","disabled"})
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function toggleStatus(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $status = $request->input('status');
        if (in_array($status, ['enabled', 'disabled'])) {
            $user->status = $status;
            $user->save();
            return response()->json(['message' => '状态更新成功']);
        }
        return response()->json(['message' => '无效的状态'], 400);
    }
}
