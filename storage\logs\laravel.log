[2025-07-22 01:45:58] local.ERROR: Required @OA\PathItem() not found {"exception":"[object] (ErrorException(code: 0): Required @OA\\PathItem() not found at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Loggers\\DefaultLogger.php:31)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Loggers\\DefaultLogger.php(31): trigger_error()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psr\\log\\src\\LoggerTrait.php(61): OpenApi\\Loggers\\DefaultLogger->log()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\OpenApi.php(157): Psr\\Log\\AbstractLogger->warning()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Analysis.php(440): OpenApi\\Annotations\\OpenApi->validate()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Generator.php(511): OpenApi\\Analysis->validate()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Generator.php(191): OpenApi\\Generator->generate()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Generator.php(132): L5Swagger\\Generator->scanFilesForDocumentation()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Console\\GenerateDocsCommand.php(72): L5Swagger\\Generator->generateDocs()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Console\\GenerateDocsCommand.php(58): L5Swagger\\Console\\GenerateDocsCommand->generateDocumentation()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): L5Swagger\\Console\\GenerateDocsCommand->handle()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#25 {main}
"} 
[2025-07-22 01:49:37] local.ERROR: $ref "#/components/schemas/" not found for @OA\Schema() in \App\Http\Controllers\Admin\AgenciesController->update() in D:\Qian_code\jingji\laravel9\laravel9\app\Http\Controllers\Admin\AgenciesController.php on line 118 {"exception":"[object] (ErrorException(code: 0): $ref \"#/components/schemas/\" not found for @OA\\Schema() in \\App\\Http\\Controllers\\Admin\\AgenciesController->update() in D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\AgenciesController.php on line 118 at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Loggers\\DefaultLogger.php:31)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(272): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Loggers\\DefaultLogger.php(31): trigger_error()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psr\\log\\src\\LoggerTrait.php(61): OpenApi\\Loggers\\DefaultLogger->log()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(511): Psr\\Log\\AbstractLogger->warning()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\Schema.php(517): OpenApi\\Annotations\\AbstractAnnotation->validate()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(592): OpenApi\\Annotations\\Schema->validate()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(565): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(592): OpenApi\\Annotations\\AbstractAnnotation->validate()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(598): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(565): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(592): OpenApi\\Annotations\\AbstractAnnotation->validate()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(565): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\Operation.php(223): OpenApi\\Annotations\\AbstractAnnotation->validate()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(592): OpenApi\\Annotations\\Operation->validate()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(565): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(592): OpenApi\\Annotations\\AbstractAnnotation->validate()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(598): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\AbstractAnnotation.php(565): OpenApi\\Annotations\\AbstractAnnotation::_validate()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Annotations\\OpenApi.php(170): OpenApi\\Annotations\\AbstractAnnotation->validate()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Analysis.php(440): OpenApi\\Annotations\\OpenApi->validate()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\zircote\\swagger-php\\src\\Generator.php(511): OpenApi\\Analysis->validate()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Generator.php(191): OpenApi\\Generator->generate()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Generator.php(132): L5Swagger\\Generator->scanFilesForDocumentation()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Console\\GenerateDocsCommand.php(72): L5Swagger\\Generator->generateDocs()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\darkaonline\\l5-swagger\\src\\Console\\GenerateDocsCommand.php(58): L5Swagger\\Console\\GenerateDocsCommand->generateDocumentation()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): L5Swagger\\Console\\GenerateDocsCommand->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#40 {main}
"} 
[2025-07-22 02:27:12] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'real_estate_management.personal_access_tokens' doesn't exist (SQL: insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values (api-token, 273156d00cb528ef3543fbca1683ca1bcd77d7c2328d18abbf575612e5eca25b, ["*"], ?, 1, App\Models\User, 2025-07-22 02:27:12, 2025-07-22 02:27:12)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'real_estate_management.personal_access_tokens' doesn't exist (SQL: insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values (api-token, 273156d00cb528ef3543fbca1683ca1bcd77d7c2328d18abbf575612e5eca25b, [\"*\"], ?, 1, App\\Models\\User, 2025-07-22 02:27:12, 2025-07-22 02:27:12)) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(322): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->Illuminate\\Database\\Eloquent\\Relations\\{closure}()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(323): tap()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(54): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\AuthController.php(46): App\\Models\\User->createToken()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AuthController->login()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'real_estate_management.personal_access_tokens' doesn't exist at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:539)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(322): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->Illuminate\\Database\\Eloquent\\Relations\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(323): tap()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(54): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\AuthController.php(46): App\\Models\\User->createToken()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AuthController->login()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#54 {main}
"} 
[2025-07-22 02:29:13] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `email` varchar(255) not null, `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\database\\migrations\\2014_10_12_000000_create_users_table.php(24): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(496): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(423): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(778): Illuminate\\Console\\View\\Components\\Task->render()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\database\\migrations\\2014_10_12_000000_create_users_table.php(24): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(496): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(423): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(778): Illuminate\\Console\\View\\Components\\Task->render()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}
"} 
[2025-07-22 02:29:58] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1071 Specified key was too long; max key length is 1000 bytes (SQL: alter table `personal_access_tokens` add index `personal_access_tokens_tokenable_type_tokenable_id_index`(`tokenable_type`, `tokenable_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1071 Specified key was too long; max key length is 1000 bytes (SQL: alter table `personal_access_tokens` add index `personal_access_tokens_tokenable_type_tokenable_id_index`(`tokenable_type`, `tokenable_id`)) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(25): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(496): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(423): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(778): Illuminate\\Console\\View\\Components\\Task->render()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1071 Specified key was too long; max key length is 1000 bytes at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(25): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(496): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(423): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(778): Illuminate\\Console\\View\\Components\\Task->render()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}
"} 
[2025-07-22 02:30:35] local.ERROR: Class "App\Providers\Schema" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\Schema\" not found at D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Providers\\AppServiceProvider.php:27)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-22 02:31:02] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'personal_access_tokens' already exists (SQL: create table `personal_access_tokens` (`id` bigint unsigned not null auto_increment primary key, `tokenable_type` varchar(191) not null, `tokenable_id` bigint unsigned not null, `name` varchar(191) not null, `token` varchar(64) not null, `abilities` text null, `last_used_at` timestamp null, `expires_at` timestamp null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'personal_access_tokens' already exists (SQL: create table `personal_access_tokens` (`id` bigint unsigned not null auto_increment primary key, `tokenable_type` varchar(191) not null, `tokenable_id` bigint unsigned not null, `name` varchar(191) not null, `token` varchar(64) not null, `abilities` text null, `last_used_at` timestamp null, `expires_at` timestamp null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(25): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(496): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(423): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(778): Illuminate\\Console\\View\\Components\\Task->render()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'personal_access_tokens' already exists at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(25): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(496): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(423): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(778): Illuminate\\Console\\View\\Components\\Task->render()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}
"} 
[2025-07-22 06:38:53] local.ERROR: Internal GD font () not available. Use only 1-5. {"exception":"[object] (Intervention\\Image\\Exception\\NotSupportedException(code: 0): Internal GD font () not available. Use only 1-5. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\Gd\\Font.php:31)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\Gd\\Font.php(46): Intervention\\Image\\Gd\\Font->getInternalFont()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\Gd\\Font.php(114): Intervention\\Image\\Gd\\Font->getInternalFontWidth()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\Gd\\Font.php(215): Intervention\\Image\\Gd\\Font->getBoxSize()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\Commands\\TextCommand.php(30): Intervention\\Image\\Gd\\Font->applyToImage()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\AbstractDriver.php(94): Intervention\\Image\\Commands\\TextCommand->execute()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\intervention\\image\\src\\Intervention\\Image\\Image.php(108): Intervention\\Image\\AbstractDriver->executeCommand()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Services\\CaptchaService.php(43): Intervention\\Image\\Image->__call()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CaptchaController.php(40): App\\Services\\CaptchaService->generateCaptcha()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CaptchaController->generate()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#46 {main}
"} 
[2025-07-22 08:57:41] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\Authenticate.php(18): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): App\\Http\\Middleware\\Authenticate->redirectTo()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(68): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#32 {main}
"} 
[2025-07-22 13:14:09] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\Authenticate.php(18): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): App\\Http\\Middleware\\Authenticate->redirectTo()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(68): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#32 {main}
"} 
[2025-07-22 13:30:03] local.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'name' doesn't have a default value (SQL: insert into `agencies` (`user_id`, `status`, `updated_at`, `created_at`) values (2, pending, 2025-07-22 13:30:03, 2025-07-22 13:30:03)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'name' doesn't have a default value (SQL: insert into `agencies` (`user_id`, `status`, `updated_at`, `created_at`) values (2, pending, 2025-07-22 13:30:03, 2025-07-22 13:30:03)) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\ProfileController.php(215): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\ProfileController->upload()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'name' doesn't have a default value at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\ProfileController.php(215): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\ProfileController->upload()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#54 {main}
"} 
[2025-07-22 13:31:28] local.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'org_code' doesn't have a default value (SQL: insert into `agencies` (`user_id`, `status`, `updated_at`, `created_at`) values (2, pending, 2025-07-22 13:31:28, 2025-07-22 13:31:28)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'org_code' doesn't have a default value (SQL: insert into `agencies` (`user_id`, `status`, `updated_at`, `created_at`) values (2, pending, 2025-07-22 13:31:28, 2025-07-22 13:31:28)) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\ProfileController.php(215): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\ProfileController->upload()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'org_code' doesn't have a default value at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\ProfileController.php(215): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\ProfileController->upload()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#54 {main}
"} 
[2025-07-23 01:14:49] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(474): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(375): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(185): Illuminate\\Routing\\Pipeline->handleException()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#31 {main}
"} 
[2025-07-23 01:23:00] local.ERROR: There are no commands defined in the "stroge" namespace.

Did you mean this?
    storage {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"stroge\" namespace.

Did you mean this?
    storage at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php:624)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(675): Symfony\\Component\\Console\\Application->findNamespace()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#6 {main}
"} 
[2025-07-23 15:56:51] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(474): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(375): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(185): Illuminate\\Routing\\Pipeline->handleException()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#31 {main}
"} 
[2025-07-23 16:13:36] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(474): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(375): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(185): Illuminate\\Routing\\Pipeline->handleException()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#31 {main}
"} 
[2025-07-23 16:15:02] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(474): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(375): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(185): Illuminate\\Routing\\Pipeline->handleException()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#31 {main}
"} 
[2025-07-23 16:15:19] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(474): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(375): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(185): Illuminate\\Routing\\Pipeline->handleException()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#31 {main}
"} 
[2025-07-23 16:16:06] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:467)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(826): Illuminate\\Routing\\UrlGenerator->route()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(474): route()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(375): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(185): Illuminate\\Routing\\Pipeline->handleException()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#31 {main}
"} 
[2025-07-23 16:30:53] local.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'user_id' doesn't have a default value (SQL: insert into `brokers` (`name`, `id_card`, `phone`, `certificate_type`, `certificate_number`, `id_card_image`, `certificate_image`, `agency_id`, `status`, `updated_at`, `created_at`) values (123, 3312312, 1323, broker, 3123123, /storage/id_card/broker_id_card_4rEeXA8mxo.png, /storage/certificate/broker_cert_GlvlZjSuGi.png, 1, pending, 2025-07-23 16:30:53, 2025-07-23 16:30:53)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'user_id' doesn't have a default value (SQL: insert into `brokers` (`name`, `id_card`, `phone`, `certificate_type`, `certificate_number`, `id_card_image`, `certificate_image`, `agency_id`, `status`, `updated_at`, `created_at`) values (123, 3312312, 1323, broker, 3123123, /storage/id_card/broker_id_card_4rEeXA8mxo.png, /storage/certificate/broker_cert_GlvlZjSuGi.png, 1, pending, 2025-07-23 16:30:53, 2025-07-23 16:30:53)) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(986): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(987): tap()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\BrokersController.php(83): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\BrokersController->store()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckAgencyProfileStatus.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckAgencyProfileStatus->handle()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#59 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#60 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'user_id' doesn't have a default value at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(986): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(987): tap()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\BrokersController.php(83): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\BrokersController->store()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckAgencyProfileStatus.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckAgencyProfileStatus->handle()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#59 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#60 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#61 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#62 {main}
"} 
[2025-07-23 16:39:56] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'updated_at' in 'field list' (SQL: insert into `broker_histories` (`broker_id`, `agency_id`, `id_card`, `action`, `details`, `updated_at`, `created_at`) values (1, 1, 123, add, "{\"name\":\"123\",\"id_card\":\"123\",\"phone\":\"15668266022\",\"certificate_type\":\"broker\",\"certificate_number\":\"123\",\"id_card_image\":\"\\\/storage\\\/id_card\\\/broker_id_card_KCv5FubHFh.png\",\"certificate_image\":\"\\\/storage\\\/certificate\\\/broker_cert_bJFOQUiPaF.png\",\"agency_id\":1,\"user_id\":4,\"status\":\"pending\"}", 2025-07-23 16:39:56, 2025-07-23 16:39:56)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'updated_at' in 'field list' (SQL: insert into `broker_histories` (`broker_id`, `agency_id`, `id_card`, `action`, `details`, `updated_at`, `created_at`) values (1, 1, 123, add, \"{\\\"name\\\":\\\"123\\\",\\\"id_card\\\":\\\"123\\\",\\\"phone\\\":\\\"15668266022\\\",\\\"certificate_type\\\":\\\"broker\\\",\\\"certificate_number\\\":\\\"123\\\",\\\"id_card_image\\\":\\\"\\\\\\/storage\\\\\\/id_card\\\\\\/broker_id_card_KCv5FubHFh.png\\\",\\\"certificate_image\\\":\\\"\\\\\\/storage\\\\\\/certificate\\\\\\/broker_cert_bJFOQUiPaF.png\\\",\\\"agency_id\\\":1,\\\"user_id\\\":4,\\\"status\\\":\\\"pending\\\"}\", 2025-07-23 16:39:56, 2025-07-23 16:39:56)) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(986): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(987): tap()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\BrokersController.php(155): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): App\\Http\\Controllers\\Agency\\BrokersController->App\\Http\\Controllers\\Agency\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(469): Illuminate\\Database\\Connection->transaction()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\DatabaseManager->__call()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\BrokersController.php(168): Illuminate\\Support\\Facades\\Facade::__callStatic()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\BrokersController->store()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckAgencyProfileStatus.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckAgencyProfileStatus->handle()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#61 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#64 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'updated_at' in 'field list' at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:539)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(986): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(987): tap()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\BrokersController.php(155): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): App\\Http\\Controllers\\Agency\\BrokersController->App\\Http\\Controllers\\Agency\\{closure}()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(469): Illuminate\\Database\\Connection->transaction()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\DatabaseManager->__call()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Agency\\BrokersController.php(168): Illuminate\\Support\\Facades\\Facade::__callStatic()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Agency\\BrokersController->store()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckAgencyProfileStatus.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckAgencyProfileStatus->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#63 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#64 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#65 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#66 {main}
"} 
[2025-07-23 17:01:56] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#59 {main}
"} 
[2025-07-23 17:02:00] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#59 {main}
"} 
[2025-07-23 17:02:04] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#59 {main}
"} 
[2025-07-23 17:06:14] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' (SQL: select `id`, `name`, `contact_person`, `contact_phone` from `agencies` where `agencies`.`id` in (1) and `agencies`.`deleted_at` is null) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#57 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'contact_person' in 'field list' at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(158): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(758): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(727): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(899): Illuminate\\Database\\Eloquent\\Builder->get()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\Admin\\BrokersController.php(67): Illuminate\\Database\\Eloquent\\Builder->paginate()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BrokersController->index()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Middleware\\CheckRole.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckRole->handle()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#56 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#59 {main}
"} 
[2025-08-02 08:35:28] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 2 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 2 at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#20 {main}
"} 
[2025-08-02 09:41:57] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month,
                COUNT(*) as count
             from `credit_archives' at line 4 (SQL: select 
                entity_type,
                type,
                DATE_FORMAT(created_at, "%Y-%m") as year_month,
                COUNT(*) as count
             from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:41:57 and `credit_archives`.`deleted_at` is null group by `entity_type`, `type`, `year_month` order by `year_month` asc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month,
                COUNT(*) as count
             from `credit_archives' at line 4 (SQL: select 
                entity_type,
                type,
                DATE_FORMAT(created_at, \"%Y-%m\") as year_month,
                COUNT(*) as count
             from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:41:57 and `credit_archives`.`deleted_at` is null group by `entity_type`, `type`, `year_month` order by `year_month` asc) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(159): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month,
                COUNT(*) as count
             from `credit_archives' at line 4 at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(159): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#49 {main}
"} 
[2025-08-02 09:42:28] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month,
                COUNT(*) as count
             from `credit_archives' at line 4 (SQL: select 
                entity_type,
                type,
                DATE_FORMAT(created_at, "%Y-%m") as year_month,
                COUNT(*) as count
             from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:42:28 and `credit_archives`.`deleted_at` is null group by entity_type, type, DATE_FORMAT(created_at, "%Y-%m") order by DATE_FORMAT(created_at, "%Y-%m")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month,
                COUNT(*) as count
             from `credit_archives' at line 4 (SQL: select 
                entity_type,
                type,
                DATE_FORMAT(created_at, \"%Y-%m\") as year_month,
                COUNT(*) as count
             from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:42:28 and `credit_archives`.`deleted_at` is null group by entity_type, type, DATE_FORMAT(created_at, \"%Y-%m\") order by DATE_FORMAT(created_at, \"%Y-%m\")) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(159): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month,
                COUNT(*) as count
             from `credit_archives' at line 4 at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(159): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#49 {main}
"} 
[2025-08-02 09:42:57] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month, COUNT(*) as count from `credit_archives` where `created_at` >= ? and' at line 1 (SQL: select entity_type, type, DATE_FORMAT(created_at, "%Y-%m") as year_month, COUNT(*) as count from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:42:57 and `credit_archives`.`deleted_at` is null group by entity_type, type, DATE_FORMAT(created_at, "%Y-%m") order by DATE_FORMAT(created_at, "%Y-%m")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month, COUNT(*) as count from `credit_archives` where `created_at` >= ? and' at line 1 (SQL: select entity_type, type, DATE_FORMAT(created_at, \"%Y-%m\") as year_month, COUNT(*) as count from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:42:57 and `credit_archives`.`deleted_at` is null group by entity_type, type, DATE_FORMAT(created_at, \"%Y-%m\") order by DATE_FORMAT(created_at, \"%Y-%m\")) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(154): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month, COUNT(*) as count from `credit_archives` where `created_at` >= ? and' at line 1 at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(154): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#49 {main}
"} 
[2025-08-02 09:43:25] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month, COUNT(*) as count from `credit_archives` where `created_at` >= ? and' at line 1 (SQL: select entity_type, type, DATE_FORMAT(created_at, "%Y-%m") as year_month, COUNT(*) as count from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:43:25 and `credit_archives`.`deleted_at` is null group by `entity_type`, `type`, DATE_FORMAT(created_at, "%Y-%m") order by DATE_FORMAT(created_at, "%Y-%m")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month, COUNT(*) as count from `credit_archives` where `created_at` >= ? and' at line 1 (SQL: select entity_type, type, DATE_FORMAT(created_at, \"%Y-%m\") as year_month, COUNT(*) as count from `credit_archives` where `created_at` >= 2025-02-01 00:00:00 and `created_at` <= 2025-08-02 09:43:25 and `credit_archives`.`deleted_at` is null group by `entity_type`, `type`, DATE_FORMAT(created_at, \"%Y-%m\") order by DATE_FORMAT(created_at, \"%Y-%m\")) at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(155): Illuminate\\Database\\Eloquent\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'year_month, COUNT(*) as count from `credit_archives` where `created_at` >= ? and' at line 1 at D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare()
#1 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#3 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#4 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#5 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#9 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 D:\\Qian_code\\jingji\\laravel9\\laravel9\\app\\Http\\Controllers\\CMS\\CreditArchiveController.php(155): Illuminate\\Database\\Eloquent\\Builder->get()
#11 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\CMS\\CreditArchiveController->statistics()
#12 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#16 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#19 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#21 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#22 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#23 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#25 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#27 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#28 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#29 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#30 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#38 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#46 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#47 D:\\Qian_code\\jingji\\laravel9\\laravel9\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#48 D:\\Qian_code\\jingji\\laravel9\\laravel9\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#49 {main}
"} 
