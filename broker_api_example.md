# 经纪人管理 API 使用示例

## 1. 添加经纪人

### 请求
```http
POST /api/agency/brokers
Content-Type: multipart/form-data
Authorization: Bearer {your_token}

name: 张三
id_card: 110101199001011234
phone: 13800138001
certificate_type: broker
certificate_number: BR2024001
id_card_image: [身份证图片文件]
certificate_image: [证书图片文件]
```

### 响应
```json
{
    "message": "添加成功，等待审核",
    "broker": {
        "id": 1,
        "agency_id": 1,
        "user_id": 5,
        "name": "张三",
        "id_card": "110101199001011234",
        "phone": "13800138001",
        "certificate_type": "broker",
        "certificate_number": "BR2024001",
        "status": "pending",
        "created_at": "2025-07-23T16:30:53.000000Z",
        "updated_at": "2025-07-23T16:30:53.000000Z"
    },
    "user": {
        "id": 5,
        "phone": "13800138001",
        "default_password": "e4da3b7fbbce2345d7772b0674a318d5",
        "password_hint": "默认密码为用户ID的MD5值：e4da3b7fbbce2345d7772b0674a318d5"
    }
}
```

## 2. 经纪人登录

经纪人可以使用手机号和默认密码登录：

### 请求
```http
POST /api/broker/login
Content-Type: application/json

{
    "phone": "13800138001",
    "password": "e4da3b7fbbce2345d7772b0674a318d5"
}
```

### 响应
```json
{
    "token": "5|abc123...",
    "user": {
        "id": 5,
        "phone": "13800138001",
        "role": "broker",
        "status": "pending"
    }
}
```

## 3. 管理员审核经纪人

### 请求
```http
POST /api/admin/brokers/1/verify
Content-Type: application/json
Authorization: Bearer {admin_token}

{
    "status": "approved"
}
```

### 响应
```json
{
    "message": "审核通过成功",
    "broker_status": "approved",
    "user_status": "approved"
}
```

## 4. 流程说明

1. **机构管理员添加经纪人**：
   - 填写经纪人基本信息
   - 上传身份证和证书图片
   - 系统自动创建用户账号
   - 默认密码为用户ID的MD5值

2. **经纪人账号状态**：
   - 初始状态：`pending`（待审核）
   - 审核通过：`approved`（已通过）
   - 审核驳回：`rejected`（已驳回）

3. **经纪人登录**：
   - 使用手机号和默认密码登录
   - 登录后可以修改密码
   - 只有审核通过的经纪人才能正常使用系统功能

## 5. 错误处理

### 手机号已存在
```json
{
    "message": "该手机号已被使用"
}
```

### 身份证号已存在
```json
{
    "message": "该身份证号已被使用"
}
```

### 验证失败
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "phone": ["手机号格式不正确"],
        "id_card_image": ["身份证图片是必需的"]
    }
}
```
