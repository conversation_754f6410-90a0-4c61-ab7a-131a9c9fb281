<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BrokerHistory extends Model
{
    use HasFactory;

    // 禁用时间戳，因为表中可能没有 created_at 和 updated_at 字段
    public $timestamps = false;

    protected $fillable = ['broker_id', 'agency_id', 'id_card', 'action', 'details'];

    protected $casts = [
        'action' => 'string',
        'details' => 'array',
    ];

    public function broker()
    {
        return $this->belongsTo(Broker::class);
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }
}
