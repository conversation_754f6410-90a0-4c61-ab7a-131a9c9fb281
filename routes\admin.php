<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AgenciesController;
use App\Http\Controllers\Admin\BrokersController;
use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Admin\ArticleCategoriesController;
use App\Http\Controllers\Admin\ArticlesController;
use App\Http\Controllers\Admin\CoursesController;
use App\Http\Controllers\Admin\TrainingPlansController;
use App\Http\Controllers\Admin\CreditArchivesController;
use App\Http\Controllers\Admin\BannersController;
use App\Http\Controllers\Admin\OrdersController;
use App\Http\Controllers\Admin\StatisticsController;

Route::middleware(['auth:sanctum', 'role:super_admin'])->group(function () {
    // 中介机构管理
    Route::get('/agencies', [AgenciesController::class, 'index']);
    Route::get('/agencies/{id}', [AgenciesController::class, 'show']);
    Route::post('/agencies/{id}/verify', [AgenciesController::class, 'verify']);
    Route::put('/agencies/{id}/review', [AgenciesController::class, 'verify']); // 添加PUT方法的review路由
    Route::put('/agencies/{id}', [AgenciesController::class, 'update']);
    Route::delete('/agencies/{id}', [AgenciesController::class, 'destroy']);

    // 经纪人管理
    Route::get('/brokers', [BrokersController::class, 'index']);
    Route::get('/brokers/{id}', [BrokersController::class, 'show']);
    Route::post('/brokers/{id}/verify', [BrokersController::class, 'verify']);
    Route::get('/brokers/histories', [BrokersController::class, 'histories']);
    Route::get('/brokers/{id}/changes', [BrokersController::class, 'changes']);

    // 账号管理
    Route::get('/users', [UsersController::class, 'index']);
    Route::post('/users/{id}/reset-password', [UsersController::class, 'resetPassword']);
    Route::post('/users/{id}/toggle-status', [UsersController::class, 'toggleStatus']);

    // CMS 文章管理
    Route::resource('/article-categories', ArticleCategoriesController::class);
    Route::resource('/articles', ArticlesController::class);

    // 课程管理
    Route::resource('/courses', CoursesController::class);
    Route::resource('/courses/{course}/videos', CoursesController::class);
    Route::post('/courses/{id}/pricing', [CoursesController::class, 'pricing']);

    // 培训计划管理
    Route::resource('/training-plans', TrainingPlansController::class);
    Route::post('/training-plans/{id}/assign', [TrainingPlansController::class, 'assign']);
    Route::get('/training-plans/{id}/courses', [TrainingPlansController::class, 'courses']);

    // 信用档案管理
    Route::resource('/credit-archives', CreditArchivesController::class);
    Route::get('/credit-archives/{type}/{entity}', [CreditArchivesController::class, 'filter']);

    // 广告横幅管理
    Route::resource('/banners', BannersController::class);
    Route::post('/banners/sort', [BannersController::class, 'sort']);

    // 订单管理
    Route::get('/orders', [OrdersController::class, 'index']);
    Route::get('/orders/{id}', [OrdersController::class, 'show']);

    // 数据统计
    Route::get('/statistics', [StatisticsController::class, 'index']);
});
