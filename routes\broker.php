<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Broker\AuthController;
use App\Http\Controllers\Broker\TrainingPlansController;
use App\Http\Controllers\Broker\CoursesController;
use App\Http\Controllers\Broker\CertificatesController;
use App\Http\Controllers\Broker\CreditArchivesController;

Route::prefix('broker')->group(function () {
    // 登录与密码管理
    Route::post('/login', [AuthController::class, 'login']);
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/change-password', [AuthController::class, 'changePassword']);

        // 培训缴费
        Route::get('/training-plans', [TrainingPlansController::class, 'index']);
        Route::post('/training-plans/{id}/pay', [TrainingPlansController::class, 'pay']);

        // 课程学习
        Route::get('/courses', [CoursesController::class, 'index']);
        Route::get('/courses/{id}/videos', [CoursesController::class, 'videos']);
        Route::post('/videos/{id}/progress', [CoursesController::class, 'saveProgress']);

        // 证书查看
        Route::get('/certificates', [CertificatesController::class, 'index']);
        Route::get('/certificates/{id}/qrcode', [CertificatesController::class, 'qrcode']);

        // 信用档案查看
        Route::get('/credit-archives', [CreditArchivesController::class, 'index']);
    });
});
