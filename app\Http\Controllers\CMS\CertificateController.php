<?php

namespace App\Http\Controllers\CMS;

use App\Http\Controllers\Controller;
use App\Models\Certificate;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="CMS-Certificates",
 *     description="CMS端证书验证"
 * )
 */
class CertificateController extends Controller
{
    /**
     * @OA\Get(
     *     path="/cms/certificates/verify/{id}",
     *     summary="证书验证",
     *     tags={"CMS-Certificates"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="证书ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功，返回证书详情")
     * )
     */
    public function verify($id)
    {
        $certificate = Certificate::with('broker', 'trainingPlan')->findOrFail($id);
        return response()->json($certificate);
    }
}
