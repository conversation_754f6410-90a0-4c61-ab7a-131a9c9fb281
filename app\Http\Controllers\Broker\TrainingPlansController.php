<?php

namespace App\Http\Controllers\Broker;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\TrainingPlan;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Broker-TrainingPlans",
 *     description="经纪人端培训缴费"
 * )
 */
class TrainingPlansController extends Controller
{
    /**
     * @OA\Get(
     *     path="/broker/training-plans",
     *     summary="经纪人培训计划列表",
     *     tags={"Broker-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $orders = Order::where('broker_id', $broker->id)->with('trainingPlan')->paginate(20);
        return response()->json($orders);
    }

    /**
     * @OA\Post(
     *     path="/broker/training-plans/{id}/pay",
     *     summary="经纪人支付培训费用",
     *     tags={"Broker-TrainingPlans"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="培训计划ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function pay(Request $request, $id)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $order = Order::where('broker_id', $broker->id)->where('training_plan_id', $id)->first();
        if (!$order) {
            return response()->json(['message' => '订单不存在'], 404);
        }
        if ($order->status !== 'pending') {
            return response()->json(['message' => '订单状态不正确'], 400);
        }
        // 这里应该集成支付网关，模拟支付成功
        $order->status = 'completed';
        $order->save();
        return response()->json(['message' => '支付成功']);
    }
}
