<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class CaptchaService
{
    /**
     * 生成验证码
     *
     * @return array 包含验证码和图片数据
     */
    public function generateCaptcha()
    {
        // 生成4位随机字符（字母和数字）
        $code = strtoupper(Str::random(4));

        // 创建图片
        $image = Image::canvas(120, 40, '#ffffff');

        // 添加干扰线
        for ($i = 0; $i < 6; $i++) {
            $image->line(
                rand(0, 120), rand(0, 40),
                rand(0, 120), rand(0, 40),
                function ($draw) {
                    $draw->color('#' . str_pad(dechex(rand(0, 0xFFFFFF)), 6, '0', STR_PAD_LEFT));
                }
            );
        }

        // 添加验证码文字
        $image->text($code, 60, 20, function($font) {
            $font->file(public_path('fonts/arial.ttf'));
            $font->size(24);
            $font->color('#000000');
            $font->align('center');
            $font->valign('middle');
            $font->angle(rand(-10, 10));
        });

        // 添加噪点
        for ($i = 0; $i < 100; $i++) {
            $image->pixel('#' . str_pad(dechex(rand(0, 0xFFFFFF)), 6, '0', STR_PAD_LEFT), rand(0, 120), rand(0, 40));
        }

        // 生成唯一key
        $key = Str::random(16);

        // 将验证码存入缓存，有效期5分钟
        Cache::put('captcha_' . $key, $code, now()->addMinutes(5));

        return [
            'key' => $key,
            'image' => (string)$image->encode('data-url')
        ];
    }

    /**
     * 验证验证码
     *
     * @param string $key 验证码key
     * @param string $code 用户输入的验证码
     * @return bool
     */
    public function validateCaptcha($key, $code)
    {
        if (empty($key) || empty($code)) {
            return false;
        }

        $cacheKey = 'captcha_' . $key;
        $correctCode = Cache::get($cacheKey);

        if (!$correctCode) {
            return false;
        }

        // 验证成功后删除缓存
        Cache::forget($cacheKey);

        // 不区分大小写比较
        return strtoupper($code) === strtoupper($correctCode);
    }
}
