<?php

namespace App\Http\Controllers\CMS;

use App\Http\Controllers\Controller;
use App\Models\CreditArchive;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="CMS-CreditArchives",
 *     description="CMS端信用档案"
 * )
 */
class CreditArchiveController extends Controller
{
    /**
     * @OA\Get(
     *     path="/cms/credit-archives",
     *     summary="信用档案列表",
     *     tags={"CMS-CreditArchives"},
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         required=false,
     *         description="红榜/黑榜类型",
     *         @OA\Schema(type="string", enum={"red","black"})
     *     ),
     *     @OA\Parameter(
     *         name="entity_type",
     *         in="query",
     *         required=false,
     *         description="主体类型（agency/broker）",
     *         @OA\Schema(type="string", enum={"agency","broker"})
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $type = $request->query('type');
        $entityType = $request->query('entity_type');
        $query = CreditArchive::query();
        if ($type) {
            $query->where('type', $type);
        }
        if ($entityType) {
            $query->where('entity_type', $entityType);
        }
        $archives = $query->paginate(20);
        return response()->json($archives);
    }

    /**
     * @OA\Get(
     *     path="/cms/credit-archives/{id}",
     *     summary="信用档案详情",
     *     tags={"CMS-CreditArchives"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="档案ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show($id)
    {
        $archive = CreditArchive::findOrFail($id);
        return response()->json($archive);
    }
}
