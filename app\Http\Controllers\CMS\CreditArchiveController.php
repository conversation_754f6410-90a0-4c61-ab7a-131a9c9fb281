<?php

namespace App\Http\Controllers\CMS;

use App\Http\Controllers\Controller;
use App\Models\CreditArchive;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="CMS-CreditArchives",
 *     description="CMS端信用档案"
 * )
 */
class CreditArchiveController extends Controller
{
    /**
     * @OA\Get(
     *     path="/cms/credit-archives",
     *     summary="信用档案列表",
     *     tags={"CMS-CreditArchives"},
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         required=false,
     *         description="红榜/黑榜类型",
     *         @OA\Schema(type="string", enum={"red","black"})
     *     ),
     *     @OA\Parameter(
     *         name="entity_type",
     *         in="query",
     *         required=false,
     *         description="主体类型（agency/broker）",
     *         @OA\Schema(type="string", enum={"agency","broker"})
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $type = $request->query('type');
        $entityType = $request->query('entity_type');
        $query = CreditArchive::query();
        if ($type) {
            $query->where('type', $type);
        }
        if ($entityType) {
            $query->where('entity_type', $entityType);
        }
        $archives = $query->paginate(20);
        return response()->json($archives);
    }

    /**
     * @OA\Get(
     *     path="/cms/credit-archives/{id}",
     *     summary="信用档案详情",
     *     tags={"CMS-CreditArchives"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="档案ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show($id)
    {
        $archive = CreditArchive::findOrFail($id);
        return response()->json($archive);
    }

    /**
     * @OA\Get(
     *     path="/cms/credit-archives/statistics",
     *     summary="信用档案统计 - 近6个月红榜黑榜增长趋势",
     *     tags={"CMS-CreditArchives"},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="red_trend",
     *                 type="object",
     *                 description="红榜增长趋势",
     *                 @OA\Property(
     *                     property="agency",
     *                     type="array",
     *                     description="企业红榜趋势",
     *                     @OA\Items(
     *                         @OA\Property(property="year_month", type="string", description="年月，格式：2025-01"),
     *                         @OA\Property(property="count", type="integer", description="数量")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="broker",
     *                     type="array",
     *                     description="个人红榜趋势",
     *                     @OA\Items(
     *                         @OA\Property(property="year_month", type="string", description="年月，格式：2025-01"),
     *                         @OA\Property(property="count", type="integer", description="数量")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="black_trend",
     *                 type="object",
     *                 description="黑榜增长趋势",
     *                 @OA\Property(
     *                     property="agency",
     *                     type="array",
     *                     description="企业黑榜趋势",
     *                     @OA\Items(
     *                         @OA\Property(property="year_month", type="string", description="年月，格式：2025-01"),
     *                         @OA\Property(property="count", type="integer", description="数量")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="broker",
     *                     type="array",
     *                     description="个人黑榜趋势",
     *                     @OA\Items(
     *                         @OA\Property(property="year_month", type="string", description="年月，格式：2025-01"),
     *                         @OA\Property(property="count", type="integer", description="数量")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function statistics()
    {
        // 获取近6个月的日期范围
        $endDate = now();
        $startDate = now()->subMonths(6)->startOfMonth();

        // 生成近6个月的年月列表
        $months = [];
        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            $months[] = $current->format('Y-m');
            $current->addMonth();
        }

        // 获取近6个月的所有信用档案数据
        $archives = CreditArchive::where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->get();

        // 初始化结果数组
        $result = [
            'red_trend' => [
                'agency' => [],
                'broker' => []
            ],
            'black_trend' => [
                'agency' => [],
                'broker' => []
            ]
        ];

        // 为每个月初始化数据
        foreach ($months as $month) {
            $result['red_trend']['agency'][] = ['year_month' => $month, 'count' => 0];
            $result['red_trend']['broker'][] = ['year_month' => $month, 'count' => 0];
            $result['black_trend']['agency'][] = ['year_month' => $month, 'count' => 0];
            $result['black_trend']['broker'][] = ['year_month' => $month, 'count' => 0];
        }

        // 统计数据
        foreach ($archives as $archive) {
            $yearMonth = $archive->created_at->format('Y-m');
            $trendKey = $archive->type . '_trend';
            $entityKey = $archive->entity_type;

            // 找到对应月份的索引
            $monthIndex = array_search($yearMonth, $months);
            if ($monthIndex !== false) {
                $result[$trendKey][$entityKey][$monthIndex]['count']++;
            }
        }

        return response()->json($result);
    }
}
