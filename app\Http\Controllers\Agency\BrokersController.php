<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use App\Models\Broker;
use App\Models\BrokerHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Agency-Brokers",
 *     description="企业端经纪人管理"
 * )
 */
class BrokersController extends Controller
{
    /**
     * @OA\Get(
     *     path="/agency/brokers",
     *     summary="获取本企业经纪人列表",
     *     description="获取本企业的经纪人列表，审核通过的经纪人会显示默认密码",
     *     tags={"Agency-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer", description="当前页码"),
     *             @OA\Property(property="data", type="array", description="经纪人列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="经纪人ID"),
     *                     @OA\Property(property="name", type="string", description="姓名"),
     *                     @OA\Property(property="phone", type="string", description="手机号"),
     *                     @OA\Property(property="status", type="string", description="状态"),
     *                     @OA\Property(property="default_password", type="string", description="默认密码（仅审核通过时显示）"),
     *                     @OA\Property(property="password_hint", type="string", description="密码提示（仅审核通过时显示）"),
     *                     @OA\Property(property="user", type="object", description="用户信息",
     *                         @OA\Property(property="id", type="integer", description="用户ID"),
     *                         @OA\Property(property="phone", type="string", description="手机号"),
     *                         @OA\Property(property="status", type="string", description="用户状态")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="total", type="integer", description="总数量")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json(['message' => '企业信息不存在'], 404);
        }
        $brokers = Broker::with(['user:id,phone,status'])
            ->where('agency_id', $agency->id)
            ->paginate(20);

        // 为审核通过的经纪人添加默认密码信息
        $brokers->getCollection()->transform(function ($broker) {
            if ($broker->status === 'approved' && $broker->user) {
                $broker->default_password = md5($broker->user->id);
                $broker->password_hint = '默认密码为用户ID的MD5值：' . $broker->default_password;
            }
            return $broker;
        });

        return response()->json($brokers);
    }

    /**
     * @OA\Post(
     *     path="/agency/brokers",
     *     summary="添加经纪人",
     *     description="添加经纪人时会自动创建用户账号，默认密码为用户ID的MD5值",
     *     tags={"Agency-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"name", "id_card", "phone", "certificate_type", "certificate_number", "id_card_image", "certificate_image"},
     *                 @OA\Property(property="name", type="string", description="姓名"),
     *                 @OA\Property(property="id_card", type="string", description="身份证号"),
     *                 @OA\Property(property="phone", type="string", description="手机号"),
     *                 @OA\Property(property="certificate_type", type="string", enum={"broker","assistant","training"}, description="证书类型"),
     *                 @OA\Property(property="certificate_number", type="string", description="证书编号"),
     *                 @OA\Property(property="id_card_image", type="string", format="binary", description="身份证图片"),
     *                 @OA\Property(property="certificate_image", type="string", format="binary", description="证书图片")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="添加成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="添加成功，等待审核"),
     *             @OA\Property(property="broker", type="object"),
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer", description="用户ID"),
     *                 @OA\Property(property="phone", type="string", description="手机号"),
     *                 @OA\Property(property="default_password", type="string", description="默认密码"),
     *                 @OA\Property(property="password_hint", type="string", description="密码提示")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function store(Request $request)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json(['message' => '企业信息不存在'], 404);
        }

        // 验证请求数据
        $request->validate([
            'name' => 'required|string|max:255',
            'id_card' => 'required|string|size:18|regex:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/',
            'phone' => 'required|string|size:11|regex:/^1[3-9]\d{9}$/',
            'certificate_type' => 'required|in:broker,assistant,training',
            'certificate_number' => 'required|string|max:255',
            'id_card_image' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'certificate_image' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ], [
            'name.required' => '姓名不能为空',
            'name.max' => '姓名不能超过255个字符',
            'id_card.required' => '身份证号不能为空',
            'id_card.size' => '身份证号必须是18位',
            'id_card.regex' => '身份证号格式不正确',
            'phone.required' => '手机号不能为空',
            'phone.size' => '手机号必须是11位',
            'phone.regex' => '手机号格式不正确',
            'certificate_type.required' => '证书类型不能为空',
            'certificate_type.in' => '证书类型必须是：经纪人、助理或培训',
            'certificate_number.required' => '证书编号不能为空',
            'certificate_number.max' => '证书编号不能超过255个字符',
            'id_card_image.required' => '身份证图片不能为空',
            'id_card_image.image' => '身份证图片必须是图片格式',
            'id_card_image.mimes' => '身份证图片必须是jpeg、png或jpg格式',
            'id_card_image.max' => '身份证图片大小不能超过2MB',
            'certificate_image.required' => '证书图片不能为空',
            'certificate_image.image' => '证书图片必须是图片格式',
            'certificate_image.mimes' => '证书图片必须是jpeg、png或jpg格式',
            'certificate_image.max' => '证书图片大小不能超过2MB',
        ]);

        // 验证手机号是否已存在
        $existingUser = User::where('phone', $request->phone)->first();
        if ($existingUser) {
            return response()->json(['message' => '该手机号已被使用'], 422);
        }

        // 验证身份证号是否已存在
        $existingBroker = Broker::where('id_card', $request->id_card)->first();
        if ($existingBroker) {
            return response()->json(['message' => '该身份证号已被使用'], 422);
        }

        // 使用数据库事务确保数据一致性
        return DB::transaction(function () use ($request, $agency) {
            // 1. 先创建用户账号
            $user = User::create([
                'phone' => $request->phone,
                'password' => '', // 临时设置为空，稍后更新
                'role' => 'broker',
                'status' => 'pending' // 待审核状态
            ]);

            // 2. 使用用户ID的MD5作为默认密码
            $defaultPassword = md5($user->id);
            $user->update([
                'password' => Hash::make($defaultPassword)
            ]);

            // 3. 准备经纪人数据
            $data = $request->all();
            $data['agency_id'] = $agency->id;
            $data['user_id'] = $user->id;
            $data['status'] = 'pending';

            // 4. 处理文件上传
            if ($request->hasFile('id_card_image')) {
                $file = $request->file('id_card_image');
                $path = $file->storeAs('public/id_card', 'broker_id_card_' . Str::random(10) . '.' . $file->getClientOriginalExtension());
                $data['id_card_image'] = Storage::url($path);
            }
            if ($request->hasFile('certificate_image')) {
                $file = $request->file('certificate_image');
                $path = $file->storeAs('public/certificate', 'broker_cert_' . Str::random(10) . '.' . $file->getClientOriginalExtension());
                $data['certificate_image'] = Storage::url($path);
            }

            // 5. 创建经纪人记录
            $broker = Broker::create($data);

            // 6. 记录操作历史
            BrokerHistory::create([
                'broker_id' => $broker->id,
                'agency_id' => $agency->id,
                'id_card' => $broker->id_card,
                'action' => 'add',
                'details' => json_encode($data)
            ]);

            return response()->json([
                'message' => '添加成功，等待审核',
                'broker' => $broker,
                'user' => [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'default_password' => $defaultPassword,
                    'password_hint' => '默认密码为用户ID的MD5值：' . $defaultPassword
                ]
            ]);
        });
    }

    /**
     * @OA\Put(
     *     path="/agency/brokers/{id}",
     *     summary="编辑经纪人",
     *     tags={"Agency-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="经纪人ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="id_card", type="string"),
     *                 @OA\Property(property="phone", type="string"),
     *                 @OA\Property(property="certificate_type", type="string", enum={"broker","assistant","training"}),
     *                 @OA\Property(property="certificate_number", type="string"),
     *                 @OA\Property(property="id_card_image", type="string", format="binary"),
     *                 @OA\Property(property="certificate_image", type="string", format="binary")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function update(Request $request, $id)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json(['message' => '企业信息不存在'], 404);
        }
        $broker = Broker::where('agency_id', $agency->id)->findOrFail($id);
        $data = $request->all();
        $data['status'] = 'pending';
        if ($request->hasFile('id_card_image')) {
            $file = $request->file('id_card_image');
            $path = $file->storeAs('public/id_card', 'broker_id_card_' . Str::random(10) . '.' . $file->getClientOriginalExtension());
            $data['id_card_image'] = Storage::url($path);
        }
        if ($request->hasFile('certificate_image')) {
            $file = $request->file('certificate_image');
            $path = $file->storeAs('public/certificate', 'broker_cert_' . Str::random(10) . '.' . $file->getClientOriginalExtension());
            $data['certificate_image'] = Storage::url($path);
        }
        $broker->update($data);
        BrokerHistory::create([
            'broker_id' => $broker->id,
            'agency_id' => $agency->id,
            'id_card' => $broker->id_card,
            'action' => 'update',
            'details' => json_encode($data)
        ]);
        return response()->json(['message' => '更新成功，等待审核']);
    }

    /**
     * @OA\Delete(
     *     path="/agency/brokers/{id}",
     *     summary="删除经纪人（软删除）",
     *     tags={"Agency-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="经纪人ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="reason", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function destroy(Request $request, $id)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json(['message' => '企业信息不存在'], 404);
        }
        $broker = Broker::where('agency_id', $agency->id)->findOrFail($id);
        BrokerHistory::create([
            'broker_id' => $broker->id,
            'agency_id' => $agency->id,
            'id_card' => $broker->id_card,
            'action' => 'delete',
            'details' => json_encode(['reason' => $request->input('reason', '无')])
        ]);
        $broker->delete();
        return response()->json(['message' => '删除成功']);
    }
}
