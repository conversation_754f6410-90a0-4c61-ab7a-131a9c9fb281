<?php

namespace App\Jobs;

use App\Models\LearningProgress;
use App\Models\Broker;
use App\Models\CourseVideo;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SaveLearningProgress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $brokerId;
    protected $videoId;
    protected $progress;
    protected $completed;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($brokerId, $videoId, $progress, $completed)
    {
        $this->brokerId = $brokerId;
        $this->videoId = $videoId;
        $this->progress = $progress;
        $this->completed = $completed;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $video = CourseVideo::find($this->videoId);
        if (!$video) {
            return;
        }
        if ($this->progress < 0 || $this->progress > $video->duration) {
            return;
        }
        $existingProgress = LearningProgress::where('broker_id', $this->brokerId)
            ->where('course_id', $video->course_id)
            ->where('video_id', $this->videoId)
            ->first();
        if ($existingProgress) {
            if ($this->progress > $existingProgress->progress) {
                $existingProgress->progress = $this->progress;
            }
            if ($this->completed) {
                $existingProgress->completed = true;
            }
            $existingProgress->save();
        } else {
            LearningProgress::create([
                'broker_id' => $this->brokerId,
                'course_id' => $video->course_id,
                'video_id' => $this->videoId,
                'progress' => $this->progress,
                'completed' => $this->completed
            ]);
        }
    }
}
