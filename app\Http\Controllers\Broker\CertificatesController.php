<?php

namespace App\Http\Controllers\Broker;

use App\Http\Controllers\Controller;
use App\Models\Certificate;
use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * @OA\Tag(
 *     name="Broker-Certificates",
 *     description="经纪人端证书查看"
 * )
 */
class CertificatesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/broker/certificates",
     *     summary="经纪人证书列表",
     *     tags={"Broker-Certificates"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $certificates = Certificate::where('broker_id', $broker->id)->paginate(20);
        return response()->json($certificates);
    }

    /**
     * @OA\Get(
     *     path="/broker/certificates/{id}/qrcode",
     *     summary="经纪人证书二维码",
     *     tags={"Broker-Certificates"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="证书ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功，返回二维码图片流")
     * )
     */
    public function qrcode(Request $request, $id)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $certificate = Certificate::where('broker_id', $broker->id)->findOrFail($id);
        $qrCode = QrCode::format('png')->size(200)->generate(route('cms.certificates.verify', ['id' => $certificate->id]));
        return response($qrCode)->header('Content-Type', 'image/png');
    }
}
