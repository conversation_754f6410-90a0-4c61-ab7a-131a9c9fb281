<?php

namespace App\Http\Controllers\CMS;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="CMS-Banners",
 *     description="CMS端广告横幅"
 * )
 */
class BannerController extends Controller
{
    /**
     * @OA\Get(
     *     path="/cms/banners",
     *     summary="获取前台横幅列表",
     *     description="获取所有横幅，按排序显示，无需登录权限",
     *     tags={"CMS-Banners"},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="integer", description="横幅ID"),
     *                 @OA\Property(property="image_url", type="string", description="图片URL"),
     *                 @OA\Property(property="link_url", type="string", description="跳转链接"),
     *                 @OA\Property(property="description", type="string", description="描述"),
     *                 @OA\Property(property="order", type="integer", description="排序")
     *             )
     *         )
     *     )
     * )
     */
    public function index()
    {
        $banners = Banner::select(['id', 'image_url', 'link_url', 'description', 'order'])
            ->orderBy('order')
            ->get();
        return response()->json($banners);
    }
}
