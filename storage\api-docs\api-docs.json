{"openapi": "3.0.0", "info": {"title": "DFH管理系统 API 文档", "description": "本系统用于测试DEMO等功能。", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://127.0.0.1:8000/api", "description": "开发环境服务器"}, {"url": "/api"}], "paths": {"/admin/agencies": {"get": {"tags": ["Admin-Agencies"], "summary": "中介机构列表", "operationId": "4aac528db23591e3ffbd202d2ff61576", "parameters": [{"name": "search", "in": "query", "description": "搜索关键词（名称/机构代码/地址/法人姓名）", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "审核状态过滤", "schema": {"type": "string", "enum": ["pending", "approved", "rejected"]}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "current_page": {"type": "integer"}, "last_page": {"type": "integer"}, "per_page": {"type": "integer"}, "total": {"type": "integer"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/admin/agencies/{id}": {"get": {"tags": ["Admin-Agencies"], "summary": "中介机构详情", "operationId": "00fe05ab43cd54bef78870b43e6fe4e5", "parameters": [{"name": "id", "in": "path", "description": "机构ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "org_code": {"type": "string"}, "org_code_image": {"type": "string"}, "address": {"type": "string"}, "legal_name": {"type": "string"}, "contact": {"type": "string"}, "license_number": {"type": "string"}, "status": {"type": "string"}, "reject_reason": {"type": "string"}, "user": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "机构不存在"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Admin-Agencies"], "summary": "编辑中介机构", "operationId": "cf064edcc608739b2cb677f60a1f26ea", "parameters": [{"name": "id", "in": "path", "description": "机构ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"type": "string"}, "org_code": {"type": "string"}, "org_code_image": {"type": "string"}, "address": {"type": "string"}, "legal_name": {"type": "string"}, "contact": {"type": "string"}, "license_number": {"type": "string"}, "status": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "更新成功"}, "404": {"description": "机构不存在"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-Agencies"], "summary": "删除中介机构（软删除）", "operationId": "76ad8e40f796c20572168d113719c466", "parameters": [{"name": "id", "in": "path", "description": "机构ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除成功"}, "404": {"description": "机构不存在"}}, "security": [{"sanctum": []}]}}, "/admin/agencies/{id}/verify": {"post": {"tags": ["Admin-Agencies"], "summary": "审核中介机构", "operationId": "3543299fefc90b01575639d644118575", "parameters": [{"name": "id", "in": "path", "description": "机构ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"status": {"description": "审核状态", "type": "string", "enum": ["approved", "rejected"]}, "reject_reason": {"description": "拒绝原因（仅当status=rejected时必填）", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "审核成功"}, "400": {"description": "无效的审核状态或缺少拒绝原因"}, "404": {"description": "机构不存在"}}, "security": [{"sanctum": []}]}}, "/admin/article-categories": {"get": {"tags": ["Admin-ArticleCategories"], "summary": "获取文章分类列表", "description": "获取文章分类列表，支持分页", "operationId": "3de80ed2b90eb8cae14b79a15c92c0be", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "data": {"description": "分类列表", "type": "array", "items": {"properties": {"id": {"description": "分类ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}, "total": {"description": "总数量", "type": "integer"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-ArticleCategories"], "summary": "创建文章分类", "description": "创建新的文章分类", "operationId": "70d5d5365dd334859b8e0f2e2f6b2d3b", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name"], "properties": {"name": {"description": "分类名称", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "创建成功"}, "category": {"description": "分类信息", "type": "object"}}, "type": "object"}}}}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}}, "/admin/article-categories/{id}": {"get": {"tags": ["Admin-ArticleCategories"], "summary": "获取文章分类详情", "description": "获取指定文章分类的详细信息", "operationId": "7d70cd0a8528a748c54b7ba7aa23205f", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "分类ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}}, "404": {"description": "分类不存在"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Admin-ArticleCategories"], "summary": "更新文章分类", "description": "更新文章分类信息", "operationId": "9307f59444bec8882986959cd4218add", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name"], "properties": {"name": {"description": "分类名称", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "更新成功"}, "category": {"description": "分类信息", "type": "object"}}, "type": "object"}}}}, "404": {"description": "分类不存在"}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-ArticleCategories"], "summary": "删除文章分类", "description": "软删除文章分类", "operationId": "571d20a9bf1e938064654c72b10d5b27", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "删除成功"}}, "type": "object"}}}}, "404": {"description": "分类不存在"}}, "security": [{"sanctum": []}]}}, "/admin/articles": {"get": {"tags": ["Admin-Articles"], "summary": "获取文章列表", "description": "获取文章列表，支持分页和筛选", "operationId": "e42b4e7a07053f7f4b3785e9643e598a", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "category_id", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "integer"}}, {"name": "keyword", "in": "query", "description": "搜索关键词（标题或作者）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "data": {"description": "文章列表", "type": "array", "items": {"properties": {"id": {"description": "文章ID", "type": "integer"}, "title": {"description": "标题", "type": "string"}, "author": {"description": "作者", "type": "string"}, "summary": {"description": "摘要", "type": "string"}, "views": {"description": "阅读量", "type": "integer"}, "published_at": {"description": "发布时间", "type": "string"}, "category": {"description": "分类信息", "type": "object"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}, "total": {"description": "总数量", "type": "integer"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-Articles"], "summary": "创建文章", "description": "创建新的文章", "operationId": "1bddb887812615b0a34bf6a9b68408c4", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["title", "content", "category_id"], "properties": {"title": {"description": "标题", "type": "string"}, "content": {"description": "文章详情（富文本）", "type": "string"}, "category_id": {"description": "分类ID", "type": "integer"}, "author": {"description": "作者", "type": "string"}, "summary": {"description": "摘要", "type": "string"}, "published_at": {"description": "发布时间", "type": "string", "format": "date-time"}}, "type": "object"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "创建成功"}, "article": {"description": "文章信息", "type": "object"}}, "type": "object"}}}}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}}, "/admin/articles/{id}": {"get": {"tags": ["Admin-Articles"], "summary": "获取文章详情", "description": "获取指定文章的详细信息", "operationId": "186d1ff75f9a88195b95f4b860308295", "parameters": [{"name": "id", "in": "path", "description": "文章ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "文章ID", "type": "integer"}, "title": {"description": "标题", "type": "string"}, "content": {"description": "文章详情", "type": "string"}, "author": {"description": "作者", "type": "string"}, "summary": {"description": "摘要", "type": "string"}, "views": {"description": "阅读量", "type": "integer"}, "published_at": {"description": "发布时间", "type": "string"}, "category": {"description": "分类信息", "type": "object"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}}, "404": {"description": "文章不存在"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Admin-Articles"], "summary": "更新文章", "description": "更新文章信息", "operationId": "38e886401398279278020a11ec481029", "parameters": [{"name": "id", "in": "path", "description": "文章ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["title", "content", "category_id"], "properties": {"title": {"description": "标题", "type": "string"}, "content": {"description": "文章详情（富文本）", "type": "string"}, "category_id": {"description": "分类ID", "type": "integer"}, "author": {"description": "作者", "type": "string"}, "summary": {"description": "摘要", "type": "string"}, "published_at": {"description": "发布时间", "type": "string", "format": "date-time"}}, "type": "object"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "更新成功"}, "article": {"description": "文章信息", "type": "object"}}, "type": "object"}}}}, "404": {"description": "文章不存在"}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-Articles"], "summary": "删除文章", "description": "软删除文章", "operationId": "fd5913470a3cb31d43bf2ef086f397ce", "parameters": [{"name": "id", "in": "path", "description": "文章ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "删除成功"}}, "type": "object"}}}}, "404": {"description": "文章不存在"}}, "security": [{"sanctum": []}]}}, "/admin/banners": {"get": {"tags": ["Admin-Banners"], "summary": "获取横幅列表", "description": "获取横幅列表，支持分页", "operationId": "d6db608ec4600b913d5182697e3eaef9", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "data": {"description": "横幅列表", "type": "array", "items": {"properties": {"id": {"description": "横幅ID", "type": "integer"}, "image_url": {"description": "图片URL", "type": "string"}, "link_url": {"description": "跳转链接", "type": "string"}, "description": {"description": "描述", "type": "string"}, "order": {"description": "排序", "type": "integer"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}, "total": {"description": "总数量", "type": "integer"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-Banners"], "summary": "创建横幅", "description": "创建新的横幅，支持图片上传", "operationId": "b9a1f86ab88677965bd1eac42d84b636", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["image", "link_url", "description", "order"], "properties": {"image": {"description": "横幅图片", "type": "string", "format": "binary"}, "link_url": {"description": "跳转链接", "type": "string"}, "description": {"description": "描述", "type": "string"}, "order": {"description": "排序", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "创建成功"}, "banner": {"description": "横幅信息", "type": "object"}}, "type": "object"}}}}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}}, "/admin/banners/{id}": {"get": {"tags": ["Admin-Banners"], "summary": "获取横幅详情", "description": "获取指定横幅的详细信息", "operationId": "21fa3279a9ea1fd9108f99f90f8f6d1a", "parameters": [{"name": "id", "in": "path", "description": "横幅ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "横幅ID", "type": "integer"}, "image_url": {"description": "图片URL", "type": "string"}, "link_url": {"description": "跳转链接", "type": "string"}, "description": {"description": "描述", "type": "string"}, "order": {"description": "排序", "type": "integer"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}}, "404": {"description": "横幅不存在"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-Banners"], "summary": "更新横幅", "description": "更新横幅信息，支持图片上传。使用POST方法并添加_method=PUT参数", "operationId": "ae86045124076de071120e0d5585635f", "parameters": [{"name": "id", "in": "path", "description": "横幅ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["link_url", "description", "order"], "properties": {"_method": {"description": "HTTP方法覆盖", "type": "string", "enum": ["PUT"]}, "image": {"description": "横幅图片（可选，不上传则保持原图片）", "type": "string", "format": "binary"}, "link_url": {"description": "跳转链接", "type": "string"}, "description": {"description": "描述", "type": "string"}, "order": {"description": "排序", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "更新成功"}, "banner": {"description": "横幅信息", "type": "object"}}, "type": "object"}}}}, "404": {"description": "横幅不存在"}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-Banners"], "summary": "删除横幅", "description": "物理删除横幅及其图片文件", "operationId": "2fa6e67cf4d1ea6b0c0f8563413a2325", "parameters": [{"name": "id", "in": "path", "description": "横幅ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "删除成功"}}, "type": "object"}}}}, "404": {"description": "横幅不存在"}}, "security": [{"sanctum": []}]}}, "/admin/brokers": {"get": {"tags": ["Admin-Brokers"], "summary": "获取经纪人列表", "description": "获取所有经纪人列表，包含企业信息和用户信息", "operationId": "37993a85377a18ae945df66911360805", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "data": {"description": "经纪人列表", "type": "array", "items": {"properties": {"id": {"description": "经纪人ID", "type": "integer"}, "name": {"description": "姓名", "type": "string"}, "phone": {"description": "手机号", "type": "string"}, "status": {"description": "状态", "type": "string"}, "agency": {"description": "企业信息", "properties": {"id": {"description": "企业ID", "type": "integer"}, "name": {"description": "企业名称", "type": "string"}, "contact": {"description": "联系人", "type": "string"}, "address": {"description": "企业地址", "type": "string"}, "user": {"description": "企业管理员信息", "properties": {"id": {"description": "管理员用户ID", "type": "integer"}, "phone": {"description": "管理员手机号", "type": "string"}}, "type": "object"}}, "type": "object"}, "user": {"description": "用户信息", "properties": {"id": {"description": "用户ID", "type": "integer"}, "phone": {"description": "手机号", "type": "string"}, "status": {"description": "用户状态", "type": "string"}}, "type": "object"}}, "type": "object"}}, "total": {"description": "总数量", "type": "integer"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/admin/brokers/{id}": {"get": {"tags": ["Admin-Brokers"], "summary": "获取经纪人详情", "description": "获取经纪人详细信息，包含企业信息和用户信息", "operationId": "c7a9250f31bc5d6a31a450c8e9bde38d", "parameters": [{"name": "id", "in": "path", "description": "经纪人ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "经纪人ID", "type": "integer"}, "agency_id": {"description": "企业ID", "type": "integer"}, "user_id": {"description": "用户ID", "type": "integer"}, "name": {"description": "姓名", "type": "string"}, "id_card": {"description": "身份证号", "type": "string"}, "phone": {"description": "手机号", "type": "string"}, "certificate_type": {"description": "证书类型", "type": "string"}, "certificate_number": {"description": "证书编号", "type": "string"}, "status": {"description": "状态", "type": "string"}, "agency": {"description": "企业信息", "properties": {"id": {"description": "企业ID", "type": "integer"}, "name": {"description": "企业名称", "type": "string"}, "contact": {"description": "联系人", "type": "string"}, "address": {"description": "企业地址", "type": "string"}, "legal_name": {"description": "法人姓名", "type": "string"}, "license_number": {"description": "许可证号", "type": "string"}, "user": {"description": "企业管理员信息", "properties": {"id": {"description": "管理员用户ID", "type": "integer"}, "phone": {"description": "管理员手机号", "type": "string"}}, "type": "object"}}, "type": "object"}, "user": {"description": "经纪人用户信息", "properties": {"id": {"description": "用户ID", "type": "integer"}, "phone": {"description": "手机号", "type": "string"}, "status": {"description": "用户状态", "type": "string"}, "role": {"description": "用户角色", "type": "string"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "经纪人不存在"}}, "security": [{"sanctum": []}]}}, "/admin/brokers/{id}/verify": {"post": {"tags": ["Admin-Brokers"], "summary": "审核经纪人", "operationId": "2f0fc76b86e86848df48a065db5d2578", "parameters": [{"name": "id", "in": "path", "description": "经纪人ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["status"], "properties": {"status": {"description": "审核状态", "type": "string", "enum": ["approved", "rejected"]}, "reject_reason": {"description": "驳回原因（status为rejected时必填）", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/brokers/histories": {"get": {"tags": ["Admin-Brokers"], "summary": "经纪人流转记录", "operationId": "78af308e1ed553d949494eb7381496b2", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/brokers/{id}/changes": {"get": {"tags": ["Admin-Brokers"], "summary": "经纪人变更记录", "operationId": "674c33d8d4f27d90dacfdd74e6804d2c", "parameters": [{"name": "id", "in": "path", "description": "经纪人ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/courses": {"get": {"tags": ["Admin-Courses"], "summary": "课程列表", "operationId": "a2aaf168ffac8b76a645212321b84f91", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-Courses"], "summary": "创建课程", "operationId": "4dca4dc092480d66d754296922056501", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/courses/{id}": {"get": {"tags": ["Admin-Courses"], "summary": "课程详情", "operationId": "18dfb725994a046c287138e9102b960e", "parameters": [{"name": "id", "in": "path", "description": "课程ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Admin-Courses"], "summary": "编辑课程", "operationId": "e98d861222c495f2d5c2930dceb96818", "parameters": [{"name": "id", "in": "path", "description": "课程ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-Courses"], "summary": "删除课程", "operationId": "61121c39340b8212768e0f9da2bb2098", "parameters": [{"name": "id", "in": "path", "description": "课程ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/credit-archives": {"get": {"tags": ["Admin-CreditArchives"], "summary": "信用档案列表", "operationId": "9ae7f54e1a0a309007b5f5a2e3f7e041", "parameters": [{"name": "entity_type", "in": "query", "description": "主体类型筛选", "schema": {"type": "string", "enum": ["agency", "broker"]}}, {"name": "type", "in": "query", "description": "档案类型筛选", "schema": {"type": "string", "enum": ["red", "black"]}}, {"name": "per_page", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20, "maximum": 100, "minimum": 1}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1, "minimum": 1}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-CreditArchives"], "summary": "创建信用档案", "operationId": "474ff25ca0c68accabcab28bb0b9cca8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"entity_type": {"type": "string", "enum": ["agency", "broker"]}, "entity_id": {"type": "integer"}, "type": {"type": "string", "enum": ["red", "black"]}, "title": {"type": "string"}, "content": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/credit-archives/{id}": {"get": {"tags": ["Admin-CreditArchives"], "summary": "信用档案详情", "operationId": "320b58930764313921c1875fd1acf257", "parameters": [{"name": "id", "in": "path", "description": "档案ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Admin-CreditArchives"], "summary": "编辑信用档案", "operationId": "90a7c7cdf8ca77483acf4e8c99450878", "parameters": [{"name": "id", "in": "path", "description": "档案ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"entity_type": {"type": "string", "enum": ["agency", "broker"]}, "entity_id": {"type": "integer"}, "type": {"type": "string", "enum": ["red", "black"]}, "title": {"type": "string"}, "content": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-CreditArchives"], "summary": "删除信用档案", "operationId": "a7c17e94f2e81f224226fee365fe7757", "parameters": [{"name": "id", "in": "path", "description": "档案ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/orders": {"get": {"tags": ["Admin-Orders"], "summary": "订单列表", "operationId": "5813b9f3e09989203ec3a2c6bde997e7", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/orders/{id}": {"get": {"tags": ["Admin-Orders"], "summary": "订单详情", "operationId": "1a904299da33e34429503d92e8d71b51", "parameters": [{"name": "id", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/statistics": {"get": {"tags": ["Admin-Statistics"], "summary": "统计数据", "operationId": "1dcf3289f1022da7a43af09997f9df98", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/training-plans": {"get": {"tags": ["Admin-TrainingPlans"], "summary": "培训计划列表", "operationId": "5ad2701ab5fb05455ba3100dc4b0da2c", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Admin-TrainingPlans"], "summary": "创建培训计划", "operationId": "f7fa812c603a25d314d76cea88525cfa", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "deadline": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/training-plans/{id}": {"get": {"tags": ["Admin-TrainingPlans"], "summary": "培训计划详情", "operationId": "752eda16e575f62e8f17085d3bece203", "parameters": [{"name": "id", "in": "path", "description": "培训计划ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Admin-TrainingPlans"], "summary": "编辑培训计划", "operationId": "7a14690e592cea5a3d4783685ff12aa7", "parameters": [{"name": "id", "in": "path", "description": "培训计划ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "deadline": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Admin-TrainingPlans"], "summary": "删除培训计划", "operationId": "1c5ba14ebc6a1194dadb632fe37fd358", "parameters": [{"name": "id", "in": "path", "description": "培训计划ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/users": {"get": {"tags": ["Admin-Users"], "summary": "用户列表", "operationId": "80385d45e6f2ce393b2c0808733158bf", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/users/{id}/reset-password": {"post": {"tags": ["Admin-Users"], "summary": "重置用户密码", "operationId": "dfcdd614dbd3f17c3e0007bbf78837a1", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/admin/users/{id}/toggle-status": {"post": {"tags": ["Admin-Users"], "summary": "禁用/启用用户账号", "operationId": "f130112bf9d60291c59068cac4ba8267", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "enum": ["enabled", "disabled"]}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/api/agency/register": {"post": {"tags": ["Agency-Auth"], "summary": "机构管理员注册（手机号+密码+验证码）", "operationId": "f496cf825edf1e24f1e9ea4917a25db8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"phone": {"type": "string"}, "password": {"type": "string"}, "captcha_key": {"type": "string"}, "captcha_code": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "注册成功"}, "400": {"description": "注册失败"}}}}, "/agency/brokers": {"get": {"tags": ["Agency-Brokers"], "summary": "获取本企业经纪人列表", "description": "获取本企业的经纪人列表，审核通过的经纪人会显示默认密码", "operationId": "c1d04da6f59826a74199e9168f42e589", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "data": {"description": "经纪人列表", "type": "array", "items": {"properties": {"id": {"description": "经纪人ID", "type": "integer"}, "name": {"description": "姓名", "type": "string"}, "phone": {"description": "手机号", "type": "string"}, "status": {"description": "状态", "type": "string"}, "default_password": {"description": "默认密码（仅审核通过时显示）", "type": "string"}, "password_hint": {"description": "密码提示（仅审核通过时显示）", "type": "string"}, "user": {"description": "用户信息", "properties": {"id": {"description": "用户ID", "type": "integer"}, "phone": {"description": "手机号", "type": "string"}, "status": {"description": "用户状态", "type": "string"}}, "type": "object"}}, "type": "object"}}, "total": {"description": "总数量", "type": "integer"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Agency-Brokers"], "summary": "添加经纪人", "description": "添加经纪人时会自动创建用户账号，默认密码为用户ID的MD5值", "operationId": "a04880eca9251d78d322b39c29f430d8", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["name", "id_card", "phone", "certificate_type", "certificate_number", "id_card_image", "certificate_image"], "properties": {"name": {"description": "姓名", "type": "string"}, "id_card": {"description": "身份证号", "type": "string"}, "phone": {"description": "手机号", "type": "string"}, "certificate_type": {"description": "证书类型", "type": "string", "enum": ["broker", "assistant", "training"]}, "certificate_number": {"description": "证书编号", "type": "string"}, "id_card_image": {"description": "身份证图片", "type": "string", "format": "binary"}, "certificate_image": {"description": "证书图片", "type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "添加成功，等待审核"}, "broker": {"type": "object"}, "user": {"properties": {"id": {"description": "用户ID", "type": "integer"}, "phone": {"description": "手机号", "type": "string"}, "default_password": {"description": "默认密码", "type": "string"}, "password_hint": {"description": "密码提示", "type": "string"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "验证失败"}}, "security": [{"sanctum": []}]}}, "/agency/brokers/{id}": {"put": {"tags": ["Agency-Brokers"], "summary": "编辑经纪人", "operationId": "d3586978a0a43df7d1684dbef1fa2190", "parameters": [{"name": "id", "in": "path", "description": "经纪人ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"name": {"type": "string"}, "id_card": {"type": "string"}, "phone": {"type": "string"}, "certificate_type": {"type": "string", "enum": ["broker", "assistant", "training"]}, "certificate_number": {"type": "string"}, "id_card_image": {"type": "string", "format": "binary"}, "certificate_image": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Agency-Brokers"], "summary": "删除经纪人（软删除）", "operationId": "42f1bdf6feeae3db13d9eb33d700f354", "parameters": [{"name": "id", "in": "path", "description": "经纪人ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"properties": {"reason": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/agency/credit-archives": {"get": {"tags": ["Agency-CreditArchives"], "summary": "企业及经纪人信用档案列表", "operationId": "d4f8d0f6a1f987273ef3619d2b0f7c1b", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/agency/info": {"get": {"tags": ["Agency-Profile"], "summary": "获取当前登录机构信息及首页统计数据", "operationId": "76fd81b63ea5fc001ea39dd2741a16df", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"agency": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "status": {"type": "string"}, "org_code": {"type": "string"}, "org_code_image": {"type": "string"}, "address": {"type": "string"}, "legal_name": {"type": "string"}, "contact": {"type": "string"}, "license_number": {"type": "string"}, "reject_reason": {"type": "string"}}, "type": "object"}, "stats": {"properties": {"total": {"type": "integer"}, "passed": {"type": "integer"}, "pending": {"type": "integer"}, "rejected": {"type": "integer"}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/agency/profile": {"get": {"tags": ["Agency-Profile"], "summary": "企业信息查看", "operationId": "e2e9c9715a7d5ae7e29f6a93ce9da7e9", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Agency-Profile"], "summary": "编辑机构资料（仅未填写或被拒绝时可编辑）", "operationId": "a691a6fe802409eb3321a4864296d318", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"type": "string"}, "org_code": {"type": "string"}, "org_code_image": {"description": "通过/agency/profile/upload接口获取的图片URL", "type": "string"}, "address": {"type": "string"}, "legal_name": {"type": "string"}, "contact": {"type": "string"}, "license_number": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "更新成功，等待审核"}, "403": {"description": "当前状态不允许编辑资料"}}, "security": [{"sanctum": []}]}}, "/agency/profile/upload": {"post": {"tags": ["Agency-Profile"], "summary": "上传组织机构代码证图片", "operationId": "2a65ea26b46b9ad83fb2eed8e82b2808", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"file": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/api/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "统一登录接口（支持超级管理员、机构管理员、经纪人）", "description": "统一登录接口，会根据用户角色检查账户状态", "operationId": "a3b306d14572d1f4bd6c064b3233e7b8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["phone", "password"], "properties": {"phone": {"description": "手机号", "type": "string"}, "password": {"description": "密码", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"properties": {"token": {"description": "访问令牌", "type": "string"}, "user": {"description": "用户信息", "type": "object"}, "message": {"type": "string", "example": "登录成功"}}, "type": "object"}}}}, "401": {"description": "登录失败", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "手机号或密码错误"}, "error": {"type": "string", "example": "INVALID_CREDENTIALS"}}, "type": "object"}}}}, "403": {"description": "账户状态异常", "content": {"application/json": {"schema": {"properties": {"message": {"description": "错误信息", "type": "string"}, "error": {"description": "错误代码", "type": "string", "enum": ["AGENCY_PENDING", "AGENCY_REJECTED", "ACCOUNT_PENDING", "ACCOUNT_REJECTED", "ACCOUNT_STATUS_INVALID", "INVALID_ROLE"]}, "status": {"description": "账户状态", "type": "string"}, "reject_reason": {"description": "驳回原因（仅当被驳回时存在）", "type": "string"}, "help_text": {"description": "帮助信息（仅当被驳回时存在）", "type": "string"}}, "type": "object"}}}}}}}, "/broker/login": {"post": {"tags": ["Broker-<PERSON><PERSON>"], "summary": "经纪人登录", "description": "经纪人登录接口，会检查账户状态", "operationId": "a00ef79fa0ecc73da2478337c13064a6", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["phone", "password"], "properties": {"phone": {"description": "手机号", "type": "string"}, "password": {"description": "密码", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"properties": {"token": {"description": "访问令牌", "type": "string"}, "user": {"description": "用户信息", "type": "object"}, "message": {"type": "string", "example": "登录成功"}}, "type": "object"}}}}, "401": {"description": "登录失败", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "手机号或密码错误"}, "error": {"type": "string", "example": "INVALID_CREDENTIALS"}}, "type": "object"}}}}, "403": {"description": "账户状态异常", "content": {"application/json": {"schema": {"properties": {"message": {"description": "错误信息", "type": "string"}, "error": {"description": "错误代码", "type": "string", "enum": ["ACCOUNT_PENDING", "ACCOUNT_REJECTED", "ACCOUNT_STATUS_INVALID"]}, "status": {"description": "账户状态", "type": "string"}, "reject_reason": {"description": "驳回原因（仅当error为ACCOUNT_REJECTED时存在）", "type": "string"}, "help_text": {"description": "帮助信息（仅当error为ACCOUNT_REJECTED时存在）", "type": "string"}}, "type": "object"}}}}}}}, "/broker/change-password": {"post": {"tags": ["Broker-<PERSON><PERSON>"], "summary": "经纪人修改密码", "operationId": "ed8340dc2d343f9e3d0f0ee0cb72d7ff", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"current_password": {"type": "string"}, "new_password": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/certificates": {"get": {"tags": ["Broker-Certificates"], "summary": "经纪人证书列表", "operationId": "0646acda3e70fd5c5fe3add27997b7c5", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/certificates/{id}/qrcode": {"get": {"tags": ["Broker-Certificates"], "summary": "经纪人证书二维码", "operationId": "cc706d0dae2f75c234d0368329644b3c", "parameters": [{"name": "id", "in": "path", "description": "证书ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功，返回二维码图片流"}}, "security": [{"sanctum": []}]}}, "/broker/courses": {"get": {"tags": ["Broker-Courses"], "summary": "经纪人课程列表", "operationId": "d3a8d576e906e1feae0d2b874dc7dfa8", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/courses/{id}/videos": {"get": {"tags": ["Broker-Courses"], "summary": "课程视频列表", "operationId": "98c855641220f4ab77ece1a321f1f37f", "parameters": [{"name": "id", "in": "path", "description": "课程ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/videos/{id}/progress": {"post": {"tags": ["Broker-Courses"], "summary": "保存课程视频学习进度", "operationId": "eb4a37545140c3a138c4223a7eac048b", "parameters": [{"name": "id", "in": "path", "description": "视频ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"progress": {"type": "integer"}, "completed": {"type": "boolean"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/credit-archives": {"get": {"tags": ["Broker-CreditArchives"], "summary": "经纪人信用档案列表", "operationId": "bcbb5c8817c1123c1a01ec5c63de7610", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/training-plans": {"get": {"tags": ["Broker-TrainingPlans"], "summary": "经纪人培训计划列表", "operationId": "cc5719cd436a403103dc69173f5e32ce", "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/broker/training-plans/{id}/pay": {"post": {"tags": ["Broker-TrainingPlans"], "summary": "经纪人支付培训费用", "operationId": "7f0d55fcf5b4dc0fcef82625642e7f03", "parameters": [{"name": "id", "in": "path", "description": "培训计划ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功"}}, "security": [{"sanctum": []}]}}, "/cms/categories": {"get": {"tags": ["CMS-Articles"], "summary": "获取文章分类列表", "description": "获取所有文章分类列表，无需登录权限", "operationId": "3f339150178d37bb368985719847440d", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"description": "分类ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}}}}}}, "/cms/categories/{id}": {"get": {"tags": ["CMS-Articles"], "summary": "获取文章分类详情", "description": "根据ID获取单个文章分类的详细信息，无需登录权限", "operationId": "6754fa02fdbcff0c2eba8169b0a3118d", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "分类ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}}, "404": {"description": "分类不存在"}}}}, "/cms/articles": {"get": {"tags": ["CMS-Articles"], "summary": "获取文章列表", "description": "获取已发布的文章列表，支持分页和分类筛选", "operationId": "96e6e72f277579f815cb98d8d155d155", "parameters": [{"name": "category_id", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "integer"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 20, "maximum": 100, "minimum": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "data": {"description": "文章列表", "type": "array", "items": {"properties": {"id": {"description": "文章ID", "type": "integer"}, "title": {"description": "标题", "type": "string"}, "author": {"description": "作者", "type": "string"}, "summary": {"description": "摘要", "type": "string"}, "views": {"description": "阅读量", "type": "integer"}, "published_at": {"description": "发布时间", "type": "string"}, "category": {"description": "分类信息", "type": "object"}}, "type": "object"}}, "total": {"description": "总数量", "type": "integer"}}, "type": "object"}}}}}}}, "/cms/articles/{id}": {"get": {"tags": ["CMS-Articles"], "summary": "获取文章详情", "description": "获取文章详细信息，每次访问阅读量+1", "operationId": "586dfb242f5d56ddb501c14a7689c471", "parameters": [{"name": "id", "in": "path", "description": "文章ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "文章ID", "type": "integer"}, "title": {"description": "标题", "type": "string"}, "content": {"description": "文章详情", "type": "string"}, "author": {"description": "作者", "type": "string"}, "summary": {"description": "摘要", "type": "string"}, "views": {"description": "阅读量", "type": "integer"}, "published_at": {"description": "发布时间", "type": "string"}, "category": {"description": "分类信息", "type": "object"}}, "type": "object"}}}}, "404": {"description": "文章不存在"}}}}, "/cms/categories-with-articles": {"get": {"tags": ["CMS-Articles"], "summary": "获取分类及其文章列表", "description": "获取所有分类及每个分类下的前N篇文章", "operationId": "995b601bb9a36f76af5fa37c06e41fb7", "parameters": [{"name": "per_page", "in": "query", "description": "每个分类返回的文章数量", "required": false, "schema": {"type": "integer", "default": 10, "maximum": 50, "minimum": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"description": "分类列表", "type": "array", "items": {"properties": {"id": {"description": "分类ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "articles": {"description": "文章列表", "type": "array", "items": {"properties": {"id": {"description": "文章ID", "type": "integer"}, "title": {"description": "文章标题", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}}}}}}}, "/cms/banners": {"get": {"tags": ["CMS-Banners"], "summary": "获取前台横幅列表", "description": "获取所有横幅，按排序显示，无需登录权限", "operationId": "d30e558f637540851733f2b1719a71a9", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"description": "横幅ID", "type": "integer"}, "image_url": {"description": "图片URL", "type": "string"}, "link_url": {"description": "跳转链接", "type": "string"}, "description": {"description": "描述", "type": "string"}, "order": {"description": "排序", "type": "integer"}}, "type": "object"}}}}}}}}, "/cms/certificates/verify/{id}": {"get": {"tags": ["CMS-Certificates"], "summary": "证书验证", "operationId": "56a5235294e3674735103a0f02cdad4c", "parameters": [{"name": "id", "in": "path", "description": "证书ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功，返回证书详情"}}}}, "/cms/credit-archives": {"get": {"tags": ["CMS-CreditArchives"], "summary": "信用档案列表", "operationId": "d95decd6149d5c0028c5e28d56baad16", "parameters": [{"name": "type", "in": "query", "description": "红榜/黑榜类型", "required": false, "schema": {"type": "string", "enum": ["red", "black"]}}, {"name": "entity_type", "in": "query", "description": "主体类型（agency/broker）", "required": false, "schema": {"type": "string", "enum": ["agency", "broker"]}}, {"name": "per_page", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 20, "maximum": 100, "minimum": 1}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"current_page": {"description": "当前页码", "type": "integer"}, "per_page": {"description": "每页数量", "type": "integer"}, "total": {"description": "总数量", "type": "integer"}, "data": {"description": "信用档案列表", "type": "array", "items": {"properties": {"id": {"description": "档案ID", "type": "integer"}, "entity_type": {"description": "主体类型", "type": "string"}, "entity_id": {"description": "主体ID", "type": "integer"}, "entity_name": {"description": "主体名称（企业名称或经纪人姓名）", "type": "string"}, "type": {"description": "档案类型", "type": "string"}, "title": {"description": "标题", "type": "string"}, "content": {"description": "内容", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/cms/credit-archives/{id}": {"get": {"tags": ["CMS-CreditArchives"], "summary": "信用档案详情", "operationId": "ff3ce0ed51dd535437da8da07c67fea0", "parameters": [{"name": "id", "in": "path", "description": "档案ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"id": {"description": "档案ID", "type": "integer"}, "entity_type": {"description": "主体类型", "type": "string"}, "entity_id": {"description": "主体ID", "type": "integer"}, "entity_name": {"description": "主体名称（企业名称或经纪人姓名）", "type": "string"}, "type": {"description": "档案类型", "type": "string"}, "title": {"description": "标题", "type": "string"}, "content": {"description": "内容", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}, "type": "object"}}}}}}}, "/cms/credit-archives/statistics": {"get": {"tags": ["CMS-CreditArchives"], "summary": "信用档案统计 - 近6个月红榜黑榜增长趋势", "operationId": "39fd33fb44e535912cd783ec85330ae7", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"red_trend": {"description": "红榜增长趋势", "properties": {"agency": {"description": "企业红榜趋势", "type": "array", "items": {"properties": {"year_month": {"description": "年月，格式：2025-01", "type": "string"}, "count": {"description": "数量", "type": "integer"}}, "type": "object"}}, "broker": {"description": "个人红榜趋势", "type": "array", "items": {"properties": {"year_month": {"description": "年月，格式：2025-01", "type": "string"}, "count": {"description": "数量", "type": "integer"}}, "type": "object"}}}, "type": "object"}, "black_trend": {"description": "黑榜增长趋势", "properties": {"agency": {"description": "企业黑榜趋势", "type": "array", "items": {"properties": {"year_month": {"description": "年月，格式：2025-01", "type": "string"}, "count": {"description": "数量", "type": "integer"}}, "type": "object"}}, "broker": {"description": "个人黑榜趋势", "type": "array", "items": {"properties": {"year_month": {"description": "年月，格式：2025-01", "type": "string"}, "count": {"description": "数量", "type": "integer"}}, "type": "object"}}}, "type": "object"}}, "type": "object"}}}}}}}, "/api/captcha": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取验证码", "operationId": "52ecb1d1637f1b12964b38a9ac316f57", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"key": {"description": "验证码key", "type": "string"}, "image": {"description": "验证码图片（base64）", "type": "string"}}, "type": "object"}}}}}}}, "/api/captcha/verify": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "验证验证码", "operationId": "9dce8dcd9d8c1749f5180957eb8d2253", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"key": {"type": "string"}, "code": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功"}, "400": {"description": "验证码错误"}}}}, "/upload": {"post": {"tags": ["Files"], "summary": "通用文件上传接口", "operationId": "7d364eabda0236f1c1288a0c68ef12e0", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"file": {"type": "string", "format": "binary"}, "type": {"type": "string", "default": "image", "enum": ["image", "document", "other", "agency_cert"]}, "folder": {"type": "string", "default": "common"}}, "type": "object"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}, "url": {"type": "string"}}, "type": "object"}}}}, "400": {"description": "未找到文件"}, "401": {"description": "未认证"}, "403": {"description": "无权限上传文件"}, "422": {"description": "文件类型不允许"}}, "security": [{"sanctum": []}]}}}, "components": {"securitySchemes": {"sanctum": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter token in format (Bearer <token>)", "name": "Authorization", "in": "header"}}}, "tags": [{"name": "Admin-ArticleCategories", "description": "超级管理员文章分类管理"}, {"name": "Admin-Articles", "description": "超级管理员文章管理"}, {"name": "Admin-Banners", "description": "广告横幅管理"}, {"name": "Admin-Brokers", "description": "经纪人管理"}, {"name": "Admin-Courses", "description": "课程管理"}, {"name": "Admin-CreditArchives", "description": "信用档案管理"}, {"name": "Admin-Orders", "description": "订单管理"}, {"name": "Admin-Statistics", "description": "数据统计"}, {"name": "Admin-TrainingPlans", "description": "培训计划管理"}, {"name": "Admin-Users", "description": "账号管理"}, {"name": "Agency-Auth", "description": "机构管理员注册与登录"}, {"name": "Agency-Brokers", "description": "企业端经纪人管理"}, {"name": "Agency-CreditArchives", "description": "企业端信用档案查看"}, {"name": "Agency-Profile", "description": "机构信息相关接口"}, {"name": "<PERSON><PERSON>", "description": "统一登录接口"}, {"name": "Broker-<PERSON><PERSON>", "description": "经纪人端登录与密码管理"}, {"name": "Broker-Certificates", "description": "经纪人端证书查看"}, {"name": "Broker-Courses", "description": "经纪人端课程学习"}, {"name": "Broker-CreditArchives", "description": "经纪人端信用档案查看"}, {"name": "Broker-TrainingPlans", "description": "经纪人端培训缴费"}, {"name": "CMS-Articles", "description": "CMS端文章管理"}, {"name": "CMS-Banners", "description": "CMS端广告横幅"}, {"name": "CMS-Certificates", "description": "CMS端证书验证"}, {"name": "CMS-CreditArchives", "description": "CMS端信用档案"}, {"name": "<PERSON><PERSON>", "description": "验证码接口"}, {"name": "Files", "description": "文件上传相关接口"}, {"name": "Admin-Agencies", "description": "Admin-Agencies"}]}