<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\SerializeDate;

class Agency extends Model
{
    use HasFactory, SoftDeletes, SerializeDate;

    protected $fillable = [
        'user_id', 'name', 'org_code', 'org_code_image', 'address',
        'legal_name', 'contact', 'license_number', 'status', 'reject_reason'
    ];

    protected $casts = [
        'status' => 'string',
        'deleted_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function brokers()
    {
        return $this->hasMany(Broker::class);
    }

    public function creditArchives()
    {
        return $this->morphMany(CreditArchive::class, 'entity');
    }
}
