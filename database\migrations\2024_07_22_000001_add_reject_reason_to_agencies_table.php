<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('agencies', function (Blueprint $table) {
            $table->string('reject_reason')->nullable()->comment('审核驳回原因');
        });
    }

    public function down()
    {
        Schema::table('agencies', function (Blueprint $table) {
            $table->dropColumn('reject_reason');
        });
    }
};
