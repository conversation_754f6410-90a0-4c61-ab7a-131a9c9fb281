<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Broker;
use App\Models\BrokerHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Admin-Brokers",
 *     description="经纪人管理"
 * )
 */
class BrokersController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/brokers",
     *     summary="获取经纪人列表",
     *     description="获取所有经纪人列表，包含企业信息和用户信息",
     *     tags={"Admin-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer", description="当前页码"),
     *             @OA\Property(property="data", type="array", description="经纪人列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="经纪人ID"),
     *                     @OA\Property(property="name", type="string", description="姓名"),
     *                     @OA\Property(property="phone", type="string", description="手机号"),
     *                     @OA\Property(property="status", type="string", description="状态"),
     *                     @OA\Property(property="agency", type="object", description="企业信息",
     *                         @OA\Property(property="id", type="integer", description="企业ID"),
     *                         @OA\Property(property="name", type="string", description="企业名称"),
     *                         @OA\Property(property="contact", type="string", description="联系人"),
     *                         @OA\Property(property="address", type="string", description="企业地址"),
     *                         @OA\Property(property="user", type="object", description="企业管理员信息",
     *                             @OA\Property(property="id", type="integer", description="管理员用户ID"),
     *                             @OA\Property(property="phone", type="string", description="管理员手机号")
     *                         )
     *                     ),
     *                     @OA\Property(property="user", type="object", description="用户信息",
     *                         @OA\Property(property="id", type="integer", description="用户ID"),
     *                         @OA\Property(property="phone", type="string", description="手机号"),
     *                         @OA\Property(property="status", type="string", description="用户状态")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="total", type="integer", description="总数量")
     *         )
     *     )
     * )
     */
    public function index()
    {
        $brokers = Broker::with([
                'agency:id,name,contact,address,user_id',
                'agency.user:id,phone',
                'user:id,phone,status'
            ])
            ->paginate(20);
        return response()->json($brokers);
    }

    /**
     * @OA\Get(
     *     path="/admin/brokers/{id}",
     *     summary="获取经纪人详情",
     *     description="获取经纪人详细信息，包含企业信息和用户信息",
     *     tags={"Admin-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="经纪人ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="经纪人ID"),
     *             @OA\Property(property="agency_id", type="integer", description="企业ID"),
     *             @OA\Property(property="user_id", type="integer", description="用户ID"),
     *             @OA\Property(property="name", type="string", description="姓名"),
     *             @OA\Property(property="id_card", type="string", description="身份证号"),
     *             @OA\Property(property="phone", type="string", description="手机号"),
     *             @OA\Property(property="certificate_type", type="string", description="证书类型"),
     *             @OA\Property(property="certificate_number", type="string", description="证书编号"),
     *             @OA\Property(property="status", type="string", description="状态"),
     *             @OA\Property(property="agency", type="object", description="企业信息",
     *                 @OA\Property(property="id", type="integer", description="企业ID"),
     *                 @OA\Property(property="name", type="string", description="企业名称"),
     *                 @OA\Property(property="contact", type="string", description="联系人"),
     *                 @OA\Property(property="address", type="string", description="企业地址"),
     *                 @OA\Property(property="legal_name", type="string", description="法人姓名"),
     *                 @OA\Property(property="license_number", type="string", description="许可证号"),
     *                 @OA\Property(property="user", type="object", description="企业管理员信息",
     *                     @OA\Property(property="id", type="integer", description="管理员用户ID"),
     *                     @OA\Property(property="phone", type="string", description="管理员手机号")
     *                 )
     *             ),
     *             @OA\Property(property="user", type="object", description="经纪人用户信息",
     *                 @OA\Property(property="id", type="integer", description="用户ID"),
     *                 @OA\Property(property="phone", type="string", description="手机号"),
     *                 @OA\Property(property="status", type="string", description="用户状态"),
     *                 @OA\Property(property="role", type="string", description="用户角色")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="经纪人不存在")
     * )
     */
    public function show($id)
    {
        $broker = Broker::with([
                'agency:id,name,contact,address,legal_name,license_number,user_id',
                'agency.user:id,phone',
                'user:id,phone,status,role'
            ])
            ->findOrFail($id);
        return response()->json($broker);
    }

    /**
     * @OA\Post(
     *     path="/admin/brokers/{id}/verify",
     *     summary="审核经纪人",
     *     tags={"Admin-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="经纪人ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"status"},
     *                 @OA\Property(property="status", type="string", enum={"approved","rejected"}, description="审核状态"),
     *                 @OA\Property(property="reject_reason", type="string", description="驳回原因（status为rejected时必填）")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function verify(Request $request, $id)
    {
        $broker = Broker::with('user')->findOrFail($id);
        $status = $request->input('status');

        if (!in_array($status, ['approved', 'rejected'])) {
            return response()->json(['message' => '无效的审核状态'], 400);
        }

        // 如果是驳回，需要提供驳回原因
        if ($status === 'rejected') {
            $request->validate([
                'reject_reason' => 'required|string|max:500'
            ], [
                'reject_reason.required' => '驳回时必须提供驳回原因',
                'reject_reason.max' => '驳回原因不能超过500个字符'
            ]);
        }

        return DB::transaction(function () use ($broker, $status, $request) {
            // 更新经纪人状态
            $broker->status = $status;

            // 处理驳回原因
            if ($status === 'rejected') {
                $broker->reject_reason = $request->input('reject_reason');
            } else {
                // 如果审核通过，清除之前的驳回原因
                $broker->reject_reason = null;
            }

            // 如果有关联的用户，同时更新用户状态
            if ($broker->user) {
                $broker->user->status = $status;
                $broker->user->save();
            }

            $broker->save();

            $message = $status === 'approved' ? '审核通过' : '审核驳回';
            return response()->json([
                'message' => $message . '成功',
                'broker_status' => $broker->status,
                'user_status' => $broker->user ? $broker->user->status : null,
                'reject_reason' => $status === 'rejected' ? $broker->reject_reason : null
            ]);
        });
    }

    /**
     * @OA\Get(
     *     path="/admin/brokers/histories",
     *     summary="经纪人流转记录",
     *     tags={"Admin-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function histories()
    {
        $histories = BrokerHistory::paginate(20);
        return response()->json($histories);
    }

    /**
     * @OA\Get(
     *     path="/admin/brokers/{id}/changes",
     *     summary="经纪人变更记录",
     *     tags={"Admin-Brokers"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="经纪人ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function changes($id)
    {
        $broker = Broker::findOrFail($id);
        $changes = BrokerHistory::where('broker_id', $id)->paginate(20);
        return response()->json($changes);
    }
}
