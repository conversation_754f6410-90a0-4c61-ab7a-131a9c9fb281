<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['broker_id', 'training_plan_id', 'amount', 'status'];

    protected $casts = [
        'status' => 'string',
    ];

    public function broker()
    {
        return $this->belongsTo(Broker::class);
    }

    public function trainingPlan()
    {
        return $this->belongsTo(TrainingPlan::class);
    }
}
