<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\CaptchaService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/**
 * @OA\Tag(
 *     name="Agency-Auth",
 *     description="机构管理员注册与登录"
 * )
 */
class AgencyAuthController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/agency/register",
     *     summary="机构管理员注册（手机号+密码+验证码）",
     *     tags={"Agency-Auth"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="phone", type="string"),
     *             @OA\Property(property="password", type="string"),
     *             @OA\Property(property="captcha_key", type="string"),
     *             @OA\Property(property="captcha_code", type="string")
     *         )
     *     ),
     *     @OA\Response(response=200, description="注册成功"),
     *     @OA\Response(response=400, description="注册失败")
     * )
     */
    public function register(Request $request, CaptchaService $captchaService)
    {
        $request->validate([
            'phone' => 'required|unique:users,phone',
            'password' => 'required|min:6',
            'captcha_key' => 'required',
            'captcha_code' => 'required',
        ]);

        if (!$captchaService->validateCaptcha($request->input('captcha_key'), $request->input('captcha_code'))) {
            return response()->json(['message' => '验证码错误'], 400);
        }

        $user = User::create([
            'phone' => $request->input('phone'),
            'password' => Hash::make($request->input('password')),
            'role' => 'agency_admin',
            'status' => 'pending',
        ]);

        return response()->json(['message' => '注册成功', 'user_id' => $user->id]);
    }
}
