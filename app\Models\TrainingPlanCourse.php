<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingPlanCourse extends Model
{
    use HasFactory;

    protected $fillable = ['training_plan_id', 'course_id'];

    public function trainingPlan()
    {
        return $this->belongsTo(TrainingPlan::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }
}
