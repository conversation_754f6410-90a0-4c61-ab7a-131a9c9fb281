<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CourseVideo extends Model
{
    use HasFactory;

    protected $fillable = ['course_id', 'title', 'description', 'video_url', 'duration', 'order'];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function learningProgress()
    {
        return $this->hasMany(LearningProgress::class, 'video_id');
    }
}
