<?php

namespace App\Http\Controllers\Broker;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseVideo;
use App\Models\LearningProgress;
use App\Models\Order;
use App\Jobs\SaveLearningProgress as SaveLearningProgressJob;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Broker-Courses",
 *     description="经纪人端课程学习"
 * )
 */
class CoursesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/broker/courses",
     *     summary="经纪人课程列表",
     *     tags={"Broker-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $completedOrders = Order::where('broker_id', $broker->id)->where('status', 'completed')->pluck('training_plan_id');
        $courses = Course::whereHas('trainingPlans', function ($query) use ($completedOrders) {
            $query->whereIn('training_plans.id', $completedOrders);
        })->paginate(20);
        return response()->json($courses);
    }

    /**
     * @OA\Get(
     *     path="/broker/courses/{id}/videos",
     *     summary="课程视频列表",
     *     tags={"Broker-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="课程ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function videos(Request $request, $id)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $course = Course::findOrFail($id);
        $completedOrders = Order::where('broker_id', $broker->id)->where('status', 'completed')->pluck('training_plan_id');
        $hasAccess = $course->trainingPlans()->whereIn('training_plans.id', $completedOrders)->exists();
        if (!$hasAccess) {
            return response()->json(['message' => '无权访问此课程'], 403);
        }
        $videos = CourseVideo::where('course_id', $id)->orderBy('order')->get();
        $progress = LearningProgress::where('broker_id', $broker->id)->where('course_id', $id)->get()->keyBy('video_id');
        $videos->each(function ($video) use ($progress) {
            $video->progress = isset($progress[$video->id]) ? $progress[$video->id]->progress : 0;
            $video->completed = isset($progress[$video->id]) ? $progress[$video->id]->completed : false;
        });
        return response()->json($videos);
    }

    /**
     * @OA\Post(
     *     path="/broker/videos/{id}/progress",
     *     summary="保存课程视频学习进度",
     *     tags={"Broker-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="视频ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="progress", type="integer"),
     *                 @OA\Property(property="completed", type="boolean")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function saveProgress(Request $request, $id)
    {
        $broker = $request->user()->broker;
        if (!$broker) {
            return response()->json(['message' => '经纪人信息不存在'], 404);
        }
        $video = CourseVideo::findOrFail($id);
        $completedOrders = Order::where('broker_id', $broker->id)->where('status', 'completed')->pluck('training_plan_id');
        $hasAccess = $video->course->trainingPlans()->whereIn('training_plans.id', $completedOrders)->exists();
        if (!$hasAccess) {
            return response()->json(['message' => '无权访问此视频'], 403);
        }
        $progress = $request->input('progress', 0);
        $completed = $request->input('completed', false);
        if ($progress < 0 || $progress > $video->duration) {
            return response()->json(['message' => '无效的进度值'], 400);
        }
        // 异步保存学习进度
        SaveLearningProgressJob::dispatch($broker->id, $id, $progress, $completed);
        return response()->json(['message' => '进度保存成功']);
    }
}
