<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * @OA\Tag(
 *     name="Auth",
 *     description="统一登录接口"
 * )
 */
class AuthController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/login",
     *     summary="统一登录接口（支持超级管理员、机构管理员、经纪人）",
     *     description="统一登录接口，会根据用户角色检查账户状态",
     *     tags={"Auth"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"phone", "password"},
     *                 @OA\Property(property="phone", type="string", description="手机号"),
     *                 @OA\Property(property="password", type="string", description="密码")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="登录成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="token", type="string", description="访问令牌"),
     *             @OA\Property(property="user", type="object", description="用户信息"),
     *             @OA\Property(property="message", type="string", example="登录成功")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="登录失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="手机号或密码错误"),
     *             @OA\Property(property="error", type="string", example="INVALID_CREDENTIALS")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="账户状态异常",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="错误信息"),
     *             @OA\Property(property="error", type="string", enum={"AGENCY_PENDING", "AGENCY_REJECTED", "ACCOUNT_PENDING", "ACCOUNT_REJECTED", "ACCOUNT_STATUS_INVALID", "INVALID_ROLE"}, description="错误代码"),
     *             @OA\Property(property="status", type="string", description="账户状态"),
     *             @OA\Property(property="reject_reason", type="string", description="驳回原因（仅当被驳回时存在）"),
     *             @OA\Property(property="help_text", type="string", description="帮助信息（仅当被驳回时存在）")
     *         )
     *     )
     * )
     */
    public function login(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'password' => 'required|string',
        ], [
            'phone.required' => '手机号不能为空',
            'password.required' => '密码不能为空',
        ]);

        $credentials = $request->only('phone', 'password');
        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // 根据用户角色进行不同的状态检查
            switch ($user->role) {
                case 'super_admin':
                    // 超级管理员不需要状态检查
                    break;

                case 'agency_admin':
                    // 机构管理员状态检查
                    // if ($user->status !== 'approved') {
                    //     Auth::logout();
                    //     return $this->getAgencyStatusResponse($user);
                    // }
                    break;

                case 'broker':
                    // 经纪人状态检查
                    if ($user->status !== 'approved') {
                        Auth::logout();
                        return $this->getBrokerStatusResponse($user);
                    }
                    break;

                default:
                    Auth::logout();
                    return response()->json([
                        'message' => '用户角色无效',
                        'error' => 'INVALID_ROLE'
                    ], 403);
            }

            $token = $user->createToken('api-token');
            return response()->json([
                'token' => $token->plainTextToken,
                'user' => [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'role' => $user->role,
                    'status' => $user->status,
                ],
                'message' => '登录成功'
            ]);
        }

        return response()->json([
            'message' => '手机号或密码错误',
            'error' => 'INVALID_CREDENTIALS'
        ], 401);
    }

    /**
     * 获取机构管理员状态响应
     */
    private function getAgencyStatusResponse($user)
    {
        switch ($user->status) {
            case 'pending':
                return response()->json([
                    'message' => '您的机构正在审核中，请耐心等待审核结果',
                    'error' => 'AGENCY_PENDING',
                    'status' => 'pending'
                ], 403);

            case 'rejected':
                // 获取机构信息以查看驳回原因
                $agency = $user->agency;
                $rejectReason = $agency && $agency->reject_reason ? $agency->reject_reason : '审核未通过';
                return response()->json([
                    'message' => '您的机构审核未通过，无法登录',
                    'error' => 'AGENCY_REJECTED',
                    'status' => 'rejected',
                    'reject_reason' => $rejectReason,
                    'help_text' => '如有疑问，请联系客服'
                ], 403);

            default:
                return response()->json([
                    'message' => '账户状态异常，请联系客服',
                    'error' => 'ACCOUNT_STATUS_INVALID',
                    'status' => $user->status
                ], 403);
        }
    }

    /**
     * 获取经纪人状态响应
     */
    private function getBrokerStatusResponse($user)
    {
        switch ($user->status) {
            case 'pending':
                return response()->json([
                    'message' => '您的账户正在审核中，请耐心等待审核结果',
                    'error' => 'ACCOUNT_PENDING',
                    'status' => 'pending'
                ], 403);

            case 'rejected':
                // 获取经纪人信息以查看驳回原因
                $broker = $user->broker;
                $rejectReason = $broker && $broker->reject_reason ? $broker->reject_reason : '审核未通过';
                return response()->json([
                    'message' => '您的账户审核未通过，无法登录',
                    'error' => 'ACCOUNT_REJECTED',
                    'status' => 'rejected',
                    'reject_reason' => $rejectReason,
                    'help_text' => '如有疑问，请联系您所属的机构管理员或客服'
                ], 403);

            default:
                return response()->json([
                    'message' => '账户状态异常，请联系客服',
                    'error' => 'ACCOUNT_STATUS_INVALID',
                    'status' => $user->status
                ], 403);
        }
    }
}
