<?php

namespace App\Http\Controllers\Broker;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;

/**
 * @OA\Tag(
 *     name="Broker-Auth",
 *     description="经纪人端登录与密码管理"
 * )
 */
class AuthController extends Controller
{
    /**
     * @OA\Post(
     *     path="/broker/login",
     *     summary="经纪人登录",
     *     description="经纪人登录接口，会检查账户状态",
     *     tags={"Broker-Auth"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"phone", "password"},
     *                 @OA\Property(property="phone", type="string", description="手机号"),
     *                 @OA\Property(property="password", type="string", description="密码")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="登录成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="token", type="string", description="访问令牌"),
     *             @OA\Property(property="user", type="object", description="用户信息"),
     *             @OA\Property(property="message", type="string", example="登录成功")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="登录失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="手机号或密码错误"),
     *             @OA\Property(property="error", type="string", example="INVALID_CREDENTIALS")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="账户状态异常",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="错误信息"),
     *             @OA\Property(property="error", type="string", enum={"ACCOUNT_PENDING", "ACCOUNT_REJECTED", "ACCOUNT_STATUS_INVALID"}, description="错误代码"),
     *             @OA\Property(property="status", type="string", description="账户状态"),
     *             @OA\Property(property="reject_reason", type="string", description="驳回原因（仅当error为ACCOUNT_REJECTED时存在）"),
     *             @OA\Property(property="help_text", type="string", description="帮助信息（仅当error为ACCOUNT_REJECTED时存在）")
     *         )
     *     )
     * )
     */
    public function login(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'password' => 'required|string',
        ], [
            'phone.required' => '手机号不能为空',
            'password.required' => '密码不能为空',
        ]);

        $credentials = $request->only('phone', 'password');
        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // 检查用户角色
            if ($user->role !== 'broker') {
                Auth::logout();
                return response()->json([
                    'message' => '无权访问',
                    'error' => 'ROLE_NOT_ALLOWED'
                ], 403);
            }

            // 检查用户状态并提供详细的提示信息
            switch ($user->status) {
                case 'pending':
                    Auth::logout();
                    return response()->json([
                        'message' => '您的账户正在审核中，请耐心等待审核结果',
                        'error' => 'ACCOUNT_PENDING',
                        'status' => 'pending'
                    ], 403);

                case 'rejected':
                    Auth::logout();
                    // 获取经纪人信息以查看驳回原因
                    $broker = $user->broker;
                    $rejectReason = $broker && $broker->reject_reason ? $broker->reject_reason : '审核未通过';
                    return response()->json([
                        'message' => '您的账户审核未通过，无法登录',
                        'error' => 'ACCOUNT_REJECTED',
                        'status' => 'rejected',
                        'reject_reason' => $rejectReason,
                        'help_text' => '如有疑问，请联系您所属的机构管理员或客服'
                    ], 403);

                case 'approved':
                    // 审核通过，允许登录
                    break;

                default:
                    Auth::logout();
                    return response()->json([
                        'message' => '账户状态异常，请联系客服',
                        'error' => 'ACCOUNT_STATUS_INVALID',
                        'status' => $user->status
                    ], 403);
            }

            $token = $user->createToken('broker-token');
            return response()->json([
                'token' => $token->plainTextToken,
                'user' => new UserResource($user),
                'message' => '登录成功'
            ]);
        }

        return response()->json([
            'message' => '手机号或密码错误',
            'error' => 'INVALID_CREDENTIALS'
        ], 401);
    }

    /**
     * @OA\Post(
     *     path="/broker/change-password",
     *     summary="经纪人修改密码",
     *     tags={"Broker-Auth"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="current_password", type="string"),
     *                 @OA\Property(property="new_password", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function changePassword(Request $request)
    {
        $user = $request->user();
        $currentPassword = $request->input('current_password');
        $newPassword = $request->input('new_password');
        if (!Auth::attempt(['phone' => $user->phone, 'password' => $currentPassword])) {
            return response()->json(['message' => '当前密码错误'], 401);
        }
        $user->password = bcrypt($newPassword);
        $user->save();
        // Revoke all tokens
        $user->tokens()->delete();
        // Issue new token
        $token = $user->createToken('broker-token');
        return response()->json(['message' => '密码修改成功', 'token' => $token->plainTextToken]);
    }
}
