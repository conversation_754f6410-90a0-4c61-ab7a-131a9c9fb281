<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('credit_archives', function (Blueprint $table) {
            // 添加新的 entity_name 字段
            $table->string('entity_name')->after('id')->comment('主体名称');

            // 删除旧的 entity_id 字段
            $table->dropColumn('entity_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('credit_archives', function (Blueprint $table) {
            // 恢复 entity_id 字段
            $table->unsignedBigInteger('entity_id')->after('id')->comment('主体ID');

            // 删除 entity_name 字段
            $table->dropColumn('entity_name');
        });
    }
};
