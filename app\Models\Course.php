<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $fillable = ['title', 'description'];

    public function videos()
    {
        return $this->hasMany(CourseVideo::class);
    }

    public function trainingPlans()
    {
        return $this->belongsToMany(TrainingPlan::class, 'training_plan_courses');
    }

    public function learningProgress()
    {
        return $this->hasMany(LearningProgress::class);
    }
}
