<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LearningProgress extends Model
{
    use HasFactory;

    protected $fillable = ['broker_id', 'course_id', 'video_id', 'progress', 'completed'];

    protected $casts = [
        'completed' => 'boolean',
    ];

    public function broker()
    {
        return $this->belongsTo(Broker::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function video()
    {
        return $this->belongsTo(CourseVideo::class, 'video_id');
    }
}
