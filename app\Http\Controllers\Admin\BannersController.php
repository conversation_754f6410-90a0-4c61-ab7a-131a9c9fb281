<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Admin-Banners",
 *     description="广告横幅管理"
 * )
 */
class BannersController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/banners",
     *     summary="获取横幅列表",
     *     description="获取横幅列表，支持分页",
     *     tags={"Admin-Banners"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer", description="当前页码"),
     *             @OA\Property(property="data", type="array", description="横幅列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="横幅ID"),
     *                     @OA\Property(property="image_url", type="string", description="图片URL"),
     *                     @OA\Property(property="link_url", type="string", description="跳转链接"),
     *                     @OA\Property(property="description", type="string", description="描述"),
     *                     @OA\Property(property="order", type="integer", description="排序"),
     *                     @OA\Property(property="created_at", type="string", description="创建时间"),
     *                     @OA\Property(property="updated_at", type="string", description="更新时间")
     *                 )
     *             ),
     *             @OA\Property(property="total", type="integer", description="总数量")
     *         )
     *     )
     * )
     */
    public function index()
    {
        $banners = Banner::orderBy('order')->paginate(20);
        return response()->json($banners);
    }

    /**
     * @OA\Post(
     *     path="/admin/banners",
     *     summary="创建横幅",
     *     description="创建新的横幅，支持图片上传",
     *     tags={"Admin-Banners"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"image", "link_url", "description", "order"},
     *                 @OA\Property(property="image", type="string", format="binary", description="横幅图片"),
     *                 @OA\Property(property="link_url", type="string", description="跳转链接"),
     *                 @OA\Property(property="description", type="string", description="描述"),
     *                 @OA\Property(property="order", type="integer", description="排序")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="创建成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="创建成功"),
     *             @OA\Property(property="banner", type="object", description="横幅信息")
     *         )
     *     ),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function store(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link_url' => 'required|url|max:255',
            'description' => 'required|string|max:255',
            'order' => 'required|integer|min:0'
        ], [
            'image.required' => '请上传横幅图片',
            'image.image' => '文件必须是图片格式',
            'image.mimes' => '图片格式必须是 jpeg, png, jpg 或 gif',
            'image.max' => '图片大小不能超过 2MB',
            'link_url.required' => '跳转链接不能为空',
            'link_url.url' => '跳转链接格式不正确',
            'description.required' => '描述不能为空',
            'description.max' => '描述不能超过255个字符',
            'order.required' => '排序不能为空',
            'order.integer' => '排序必须是整数',
            'order.min' => '排序不能小于0'
        ]);

        // 处理图片上传
        $file = $request->file('image');
        $filename = 'banner_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('public/banners', $filename);
        $imageUrl = Storage::url($path);

        $banner = Banner::create([
            'image_url' => $imageUrl,
            'link_url' => $request->link_url,
            'description' => $request->description,
            'order' => $request->order
        ]);

        return response()->json([
            'message' => '创建成功',
            'banner' => $banner
        ]);
    }

    /**
     * @OA\Get(
     *     path="/admin/banners/{id}",
     *     summary="获取横幅详情",
     *     description="获取指定横幅的详细信息",
     *     tags={"Admin-Banners"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="横幅ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="横幅ID"),
     *             @OA\Property(property="image_url", type="string", description="图片URL"),
     *             @OA\Property(property="link_url", type="string", description="跳转链接"),
     *             @OA\Property(property="description", type="string", description="描述"),
     *             @OA\Property(property="order", type="integer", description="排序"),
     *             @OA\Property(property="created_at", type="string", description="创建时间"),
     *             @OA\Property(property="updated_at", type="string", description="更新时间")
     *         )
     *     ),
     *     @OA\Response(response=404, description="横幅不存在")
     * )
     */
    public function show($id)
    {
        $banner = Banner::findOrFail($id);
        return response()->json($banner);
    }

    /**
     * @OA\Post(
     *     path="/admin/banners/{id}",
     *     summary="更新横幅",
     *     description="更新横幅信息，支持图片上传。使用POST方法并添加_method=PUT参数",
     *     tags={"Admin-Banners"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="横幅ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"link_url", "description", "order"},
     *                 @OA\Property(property="_method", type="string", enum={"PUT"}, description="HTTP方法覆盖"),
     *                 @OA\Property(property="image", type="string", format="binary", description="横幅图片（可选，不上传则保持原图片）"),
     *                 @OA\Property(property="link_url", type="string", description="跳转链接"),
     *                 @OA\Property(property="description", type="string", description="描述"),
     *                 @OA\Property(property="order", type="integer", description="排序")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="更新成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="更新成功"),
     *             @OA\Property(property="banner", type="object", description="横幅信息")
     *         )
     *     ),
     *     @OA\Response(response=404, description="横幅不存在"),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function update(Request $request, $id)
    {
        $banner = Banner::findOrFail($id);

        $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link_url' => 'required|url|max:255',
            'description' => 'required|string|max:255',
            'order' => 'required|integer|min:0'
        ], [
            'image.image' => '文件必须是图片格式',
            'image.mimes' => '图片格式必须是 jpeg, png, jpg 或 gif',
            'image.max' => '图片大小不能超过 2MB',
            'link_url.required' => '跳转链接不能为空',
            'link_url.url' => '跳转链接格式不正确',
            'description.required' => '描述不能为空',
            'description.max' => '描述不能超过255个字符',
            'order.required' => '排序不能为空',
            'order.integer' => '排序必须是整数',
            'order.min' => '排序不能小于0'
        ]);

        $data = [
            'link_url' => $request->input('link_url'),
            'description' => $request->input('description'),
            'order' => $request->input('order')
        ];

        // 如果上传了新图片，处理图片上传并删除旧图片
        if ($request->hasFile('image')) {
            // 删除旧图片
            if ($banner->image_url) {
                $oldImagePath = str_replace('/storage/', 'public/', $banner->image_url);
                Storage::delete($oldImagePath);
            }

            // 上传新图片
            $file = $request->file('image');
            $filename = 'banner_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public/banners', $filename);
            $data['image_url'] = Storage::url($path);
        }

        $banner->update($data);

        return response()->json([
            'message' => '更新成功',
            'banner' => $banner->fresh()
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/admin/banners/{id}",
     *     summary="删除横幅",
     *     description="物理删除横幅及其图片文件",
     *     tags={"Admin-Banners"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="横幅ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="删除成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="删除成功")
     *         )
     *     ),
     *     @OA\Response(response=404, description="横幅不存在")
     * )
     */
    public function destroy($id)
    {
        $banner = Banner::findOrFail($id);

        // 删除图片文件
        if ($banner->image_url) {
            $imagePath = str_replace('/storage/', 'public/', $banner->image_url);
            Storage::delete($imagePath);
        }

        // 物理删除横幅记录
        $banner->delete();

        return response()->json(['message' => '删除成功']);
    }

    public function sort(Request $request)
    {
        $order = $request->input('order');
        foreach ($order as $id => $newOrder) {
            $banner = Banner::find($id);
            if ($banner) {
                $banner->order = $newOrder;
                $banner->save();
            }
        }
        return response()->json(['message' => '排序成功']);
    }
}
