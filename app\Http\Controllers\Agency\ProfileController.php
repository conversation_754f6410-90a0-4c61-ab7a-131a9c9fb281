<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

/**
 * @OA\Tag(
 *     name="Agency-Profile",
 *     description="机构信息相关接口"
 * )
 */
class ProfileController extends Controller
{
    /**
     * @OA\Get(
     *     path="/agency/info",
     *     summary="获取当前登录机构信息及首页统计数据",
     *     tags={"Agency-Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="agency", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="org_code", type="string"),
     *                 @OA\Property(property="org_code_image", type="string"),
     *                 @OA\Property(property="address", type="string"),
     *                 @OA\Property(property="legal_name", type="string"),
     *                 @OA\Property(property="contact", type="string"),
     *                 @OA\Property(property="license_number", type="string"),
     *                 @OA\Property(property="reject_reason", type="string")
     *             ),
     *             @OA\Property(property="stats", type="object",
     *                 @OA\Property(property="total", type="integer"),
     *                 @OA\Property(property="passed", type="integer"),
     *                 @OA\Property(property="pending", type="integer"),
     *                 @OA\Property(property="rejected", type="integer")
     *             )
     *         )
     *     )
     * )
     */
    public function info(Request $request)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json([
                'status' => 'unfilled',
                'message' => '您未填写资料，请先去填写资料申请入驻',
                'agency' => null
            ], 200);
        }
        if ($agency->status === 'pending') {
            return response()->json([
                'status' => 'pending',
                'message' => '您已填写入驻申请，请耐心等待管理员审核',
                'agency' => [
                    'id' => $agency->id,
                    'name' => $agency->name,
                    'status' => $agency->status,
                    'org_code' => $agency->org_code,
                    'org_code_image' => $agency->org_code_image,
                    'address' => $agency->address,
                    'legal_name' => $agency->legal_name,
                    'contact' => $agency->contact,
                    'license_number' => $agency->license_number,
                    'reject_reason' => $agency->reject_reason,
                ]
            ], 200);
        }
        if ($agency->status === 'rejected') {
            return response()->json([
                'status' => 'rejected',
                'message' => '您之前申请入驻的资料被驳回了，您可以根据驳回原因重新填写并提交',
                'reject_reason' => $agency->reject_reason,
                'agency' => [
                    'id' => $agency->id,
                    'name' => $agency->name,
                    'status' => $agency->status,
                    'org_code' => $agency->org_code,
                    'org_code_image' => $agency->org_code_image,
                    'address' => $agency->address,
                    'legal_name' => $agency->legal_name,
                    'contact' => $agency->contact,
                    'license_number' => $agency->license_number,
                    'reject_reason' => $agency->reject_reason,
                ]
            ], 200);
        }
        // 审核通过
        $total = $agency->brokers()->count();
        $passed = $agency->brokers()->where('status', 'approved')->count();
        $pending = $agency->brokers()->where('status', 'pending')->count();
        $rejected = $agency->brokers()->where('status', 'rejected')->count();
        return response()->json([
            'status' => 'approved',
            'message' => '审核通过',
            'agency' => [
                'id' => $agency->id,
                'name' => $agency->name,
                'status' => $agency->status,
                'org_code' => $agency->org_code,
                'org_code_image' => $agency->org_code_image,
                'address' => $agency->address,
                'legal_name' => $agency->legal_name,
                'contact' => $agency->contact,
                'license_number' => $agency->license_number,
                'reject_reason' => $agency->reject_reason,
            ],
            'stats' => [
                'total' => $total,
                'passed' => $passed,
                'pending' => $pending,
                'rejected' => $rejected,
            ]
        ], 200);
    }

    /**
     * @OA\Get(
     *     path="/agency/profile",
     *     summary="企业信息查看",
     *     tags={"Agency-Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show(Request $request)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json([
                'message' => '您尚未填写企业信息，请先填写企业资料',
                'status' => 'unfilled'
            ], 200);
        }
        return response()->json($agency);
    }

    /**
     * @OA\Put(
     *     path="/agency/profile",
     *     summary="编辑机构资料（仅未填写或被拒绝时可编辑）",
     *     tags={"Agency-Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="org_code", type="string"),
     *             @OA\Property(property="org_code_image", type="string", description="通过/agency/profile/upload接口获取的图片URL"),
     *             @OA\Property(property="address", type="string"),
     *             @OA\Property(property="legal_name", type="string"),
     *             @OA\Property(property="contact", type="string"),
     *             @OA\Property(property="license_number", type="string")
     *         )
     *     ),
     *     @OA\Response(response=200, description="更新成功，等待审核"),
     *     @OA\Response(response=403, description="当前状态不允许编辑资料")
     * )
     */
    public function update(Request $request)
    {
        $agency = $request->user()->agency;
        // 只允许未填写资料或审核被拒绝时可编辑
        if ($agency && !in_array($agency->status, ['rejected', null])) {
            return response()->json(['message' => '当前状态不允许编辑资料'], 403);
        }

        // 验证必填字段
        $request->validate([
            'name' => 'required|string|max:100',
            'org_code' => 'required|string|max:50',
            'org_code_image' => 'required|string',
            'address' => 'required|string|max:200',
            'legal_name' => 'required|string|max:50',
            'contact' => 'required|string|max:20',
            'license_number' => 'required|string|max:50',
        ]);

        if (!$agency) {
            // 首次填写资料，创建agency
            $agency = $request->user()->agency()->create($request->all() + ['status' => 'pending']);
        } else {
            // 被拒绝后重新编辑资料，重置状态为pending
            $agency->update($request->all());
            $agency->status = 'pending';
            $agency->save();
        }
        return response()->json(['message' => '更新成功，等待审核']);
    }

    /**
     * @OA\Post(
     *     path="/agency/profile/upload",
     *     summary="上传组织机构代码证图片",
     *     tags={"Agency-Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="file", type="string", format="binary")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function upload(Request $request)
    {
        // 验证用户角色是否为机构管理员或经纪人
        $user = $request->user();
        if (!in_array($user->role, ['agency_admin', 'broker'])) {
            return response()->json(['message' => '无权限上传文件'], 403);
        }

        if ($request->hasFile('file')) {
            $file = $request->file('file');

            // 生成唯一的文件名
            $fileName = uniqid('org_code_') . '.' . $file->getClientOriginalExtension();

            // 存储文件
            $path = $file->storeAs('public/uploads', $fileName);

            // 返回可访问的URL
            $url = Storage::url($path);

            return response()->json([
                'message' => '上传成功',
                'url' => $url
            ]);
        }

        return response()->json(['message' => '未找到文件'], 400);
    }
}
