<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\ArticleCategory;
use Illuminate\Http\Request;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin-Articles",
 *     description="超级管理员文章管理"
 * )
 */
class ArticlesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/articles",
     *     summary="获取文章列表",
     *     description="获取文章列表，支持分页和筛选",
     *     tags={"Admin-Articles"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="category_id",
     *         in="query",
     *         description="分类ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="keyword",
     *         in="query",
     *         description="搜索关键词（标题或作者）",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer", description="当前页码"),
     *             @OA\Property(property="data", type="array", description="文章列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", description="文章ID"),
     *                     @OA\Property(property="title", type="string", description="标题"),
     *                     @OA\Property(property="author", type="string", description="作者"),
     *                     @OA\Property(property="summary", type="string", description="摘要"),
     *                     @OA\Property(property="views", type="integer", description="阅读量"),
     *                     @OA\Property(property="published_at", type="string", description="发布时间"),
     *                     @OA\Property(property="category", type="object", description="分类信息"),
     *                     @OA\Property(property="created_at", type="string", description="创建时间"),
     *                     @OA\Property(property="updated_at", type="string", description="更新时间")
     *                 )
     *             ),
     *             @OA\Property(property="total", type="integer", description="总数量")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = Article::with('category');

        // 分类筛选
        if ($request->has('category_id') && $request->category_id) {
            $query->where('category_id', $request->category_id);
        }

        // 关键词搜索
        if ($request->has('keyword') && $request->keyword) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->orWhere('author', 'like', "%{$keyword}%");
            });
        }

        $articles = $query->orderBy('created_at', 'desc')->paginate(20);
        return response()->json($articles);
    }

    /**
     * @OA\Post(
     *     path="/admin/articles",
     *     summary="创建文章",
     *     description="创建新的文章",
     *     tags={"Admin-Articles"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"title", "content", "category_id"},
     *                 @OA\Property(property="title", type="string", description="标题"),
     *                 @OA\Property(property="content", type="string", description="文章详情（富文本）"),
     *                 @OA\Property(property="category_id", type="integer", description="分类ID"),
     *                 @OA\Property(property="author", type="string", description="作者"),
     *                 @OA\Property(property="summary", type="string", description="摘要"),
     *                 @OA\Property(property="published_at", type="string", format="date-time", description="发布时间")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="创建成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="创建成功"),
     *             @OA\Property(property="article", type="object", description="文章信息")
     *         )
     *     ),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category_id' => 'required|exists:article_categories,id',
            'author' => 'nullable|string|max:100',
            'summary' => 'nullable|string|max:500',
            'published_at' => 'nullable|date'
        ], [
            'title.required' => '标题不能为空',
            'title.max' => '标题不能超过255个字符',
            'content.required' => '文章内容不能为空',
            'category_id.required' => '请选择文章分类',
            'category_id.exists' => '选择的分类不存在',
            'author.max' => '作者名称不能超过100个字符',
            'summary.max' => '摘要不能超过500个字符',
            'published_at.date' => '发布时间格式不正确'
        ]);

        $article = Article::create([
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'category_id' => $request->input('category_id'),
            'author' => $request->input('author', '管理员'),
            'summary' => $request->input('summary'),
            'views' => 0,
            'published_at' => $request->input('published_at') ?
                Carbon::parse($request->input('published_at')) :
                now()
        ]);

        return response()->json([
            'message' => '创建成功',
            'article' => $article->load('category')
        ]);
    }

    /**
     * @OA\Get(
     *     path="/admin/articles/{id}",
     *     summary="获取文章详情",
     *     description="获取指定文章的详细信息",
     *     tags={"Admin-Articles"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="文章ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="文章ID"),
     *             @OA\Property(property="title", type="string", description="标题"),
     *             @OA\Property(property="content", type="string", description="文章详情"),
     *             @OA\Property(property="author", type="string", description="作者"),
     *             @OA\Property(property="summary", type="string", description="摘要"),
     *             @OA\Property(property="views", type="integer", description="阅读量"),
     *             @OA\Property(property="published_at", type="string", description="发布时间"),
     *             @OA\Property(property="category", type="object", description="分类信息"),
     *             @OA\Property(property="created_at", type="string", description="创建时间"),
     *             @OA\Property(property="updated_at", type="string", description="更新时间")
     *         )
     *     ),
     *     @OA\Response(response=404, description="文章不存在")
     * )
     */
    public function show($id)
    {
        $article = Article::with('category')->findOrFail($id);
        return response()->json($article);
    }

    /**
     * @OA\Put(
     *     path="/admin/articles/{id}",
     *     summary="更新文章",
     *     description="更新文章信息",
     *     tags={"Admin-Articles"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="文章ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"title", "content", "category_id"},
     *                 @OA\Property(property="title", type="string", description="标题"),
     *                 @OA\Property(property="content", type="string", description="文章详情（富文本）"),
     *                 @OA\Property(property="category_id", type="integer", description="分类ID"),
     *                 @OA\Property(property="author", type="string", description="作者"),
     *                 @OA\Property(property="summary", type="string", description="摘要"),
     *                 @OA\Property(property="published_at", type="string", format="date-time", description="发布时间")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="更新成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="更新成功"),
     *             @OA\Property(property="article", type="object", description="文章信息")
     *         )
     *     ),
     *     @OA\Response(response=404, description="文章不存在"),
     *     @OA\Response(response=422, description="验证失败")
     * )
     */
    public function update(Request $request, $id)
    {
        $article = Article::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category_id' => 'required|exists:article_categories,id',
            'author' => 'nullable|string|max:100',
            'summary' => 'nullable|string|max:500',
            'published_at' => 'nullable|date'
        ], [
            'title.required' => '标题不能为空',
            'title.max' => '标题不能超过255个字符',
            'content.required' => '文章内容不能为空',
            'category_id.required' => '请选择文章分类',
            'category_id.exists' => '选择的分类不存在',
            'author.max' => '作者名称不能超过100个字符',
            'summary.max' => '摘要不能超过500个字符',
            'published_at.date' => '发布时间格式不正确'
        ]);

        $article->update([
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'category_id' => $request->input('category_id'),
            'author' => $request->input('author', $article->author),
            'summary' => $request->input('summary'),
            'published_at' => $request->input('published_at') ?
                Carbon::parse($request->input('published_at')) :
                $article->published_at
        ]);

        return response()->json([
            'message' => '更新成功',
            'article' => $article->load('category')
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/admin/articles/{id}",
     *     summary="删除文章",
     *     description="软删除文章",
     *     tags={"Admin-Articles"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="文章ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="删除成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="删除成功")
     *         )
     *     ),
     *     @OA\Response(response=404, description="文章不存在")
     * )
     */
    public function destroy($id)
    {
        $article = Article::findOrFail($id);
        $article->delete(); // 软删除
        return response()->json(['message' => '删除成功']);
    }
}
