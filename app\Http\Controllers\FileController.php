<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Files",
 *     description="文件上传相关接口"
 * )
 */
class FileController extends Controller
{
    /**
     * @OA\Post(
     *     path="/upload",
     *     summary="通用文件上传接口",
     *     tags={"Files"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="file", type="string", format="binary"),
     *                 @OA\Property(property="type", type="string", enum={"image", "document", "other", "agency_cert"}, default="image"),
     *                 @OA\Property(property="folder", type="string", default="common")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(property="url", type="string")
     *         )
     *     ),
     *     @OA\Response(response=400, description="未找到文件"),
     *     @OA\Response(response=401, description="未认证"),
     *     @OA\Response(response=403, description="无权限上传文件"),
     *     @OA\Response(response=422, description="文件类型不允许")
     * )
     */
    public function upload(Request $request)
    {
        // 验证用户是否已登录 - 实际上auth:sanctum中间件已经处理了这个检查
        // 这里只是为了确保代码逻辑完整性
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'message' => '未认证，请先登录',
                'error' => 'Unauthenticated'
            ], 401);
        }

        // 验证请求
        $request->validate([
            'file' => 'required|file|max:10240', // 最大10MB
            'type' => 'sometimes|in:image,document,other,agency_cert',
            'folder' => 'sometimes|string|max:50',
        ]);

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $type = $request->input('type', 'image');
            $folder = $request->input('folder', 'common');

            // 检查文件类型
            $allowedMimes = $this->getAllowedMimeTypes($type);
            if (!in_array($file->getMimeType(), $allowedMimes)) {
                return response()->json([
                    'message' => '文件类型不允许',
                    'mime' => $file->getMimeType(),
                    'allowed' => $allowedMimes
                ], 422);
            }

            // 生成唯一的文件名
            $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();

            // 存储路径
            $path = $file->storeAs("public/uploads/{$folder}", $fileName);

            // 返回可访问的URL
            $url = Storage::url($path);

            return response()->json([
                'message' => '上传成功',
                'url' => $url,
                'file_name' => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize()
            ]);
        }

        return response()->json(['message' => '未找到文件'], 400);
    }

    /**
     * 获取允许的MIME类型
     *
     * @param string $type
     * @return array
     */
    private function getAllowedMimeTypes($type)
    {
        switch ($type) {
            case 'image':
                return [
                    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                    'image/svg+xml', 'image/bmp'
                ];
            case 'document':
                return [
                    'application/pdf', 'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'text/plain'
                ];
            case 'agency_cert':
                // 机构证书允许的MIME类型（图片和PDF）
                return [
                    'image/jpeg', 'image/png', 'image/gif', 'application/pdf'
                ];
            case 'other':
            default:
                return [
                    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                    'image/svg+xml', 'image/bmp', 'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'text/plain', 'application/zip', 'application/x-rar-compressed'
                ];
        }
    }
}
