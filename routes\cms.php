<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CMS\ArticleController;
use App\Http\Controllers\CMS\CreditArchiveController;
use App\Http\Controllers\CMS\BannerController;
use App\Http\Controllers\CMS\CertificateController;

Route::prefix('cms')->group(function () {
    // 文章查看
    Route::get('/categories', [ArticleController::class, 'categories']);
    Route::get('/categories/{id}', [ArticleController::class, 'categoryDetail']);
    Route::get('/categories-with-articles', [ArticleController::class, 'categoriesWithArticles']);
    Route::get('/articles', [ArticleController::class, 'index']);
    Route::get('/articles/{id}', [ArticleController::class, 'show']);

    // 信用档案查看
    Route::get('/credit-archives', [CreditArchiveController::class, 'index']);
    Route::get('/credit-archives/statistics', [CreditArchiveController::class, 'statistics']);
    Route::get('/credit-archives/{id}', [CreditArchiveController::class, 'show']);

    // 广告横幅
    Route::get('/banners', [BannerController::class, 'index']);

    // 证书验证
    Route::get('/certificates/verify/{id}', [CertificateController::class, 'verify']);
});

Route::middleware('auth:sanctum')->prefix('cms')->group(function () {
    // 需要认证才能访问的CMS功能
    Route::get('/my-articles', [ArticleController::class, 'myArticles']);
});
