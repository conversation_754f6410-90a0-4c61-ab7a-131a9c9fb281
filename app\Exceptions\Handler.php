<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Auth\AuthenticationException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Convert an authentication exception into a response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Auth\AuthenticationException  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        // 对于 API 请求，返回 JSON 响应而不是重定向
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'message' => '未认证，请先登录',
                'error' => 'Unauthenticated'
            ], 401);
        }

        // 对于 Web 请求，检查是否有登录路由
        try {
            return redirect()->guest(route('login'));
        } catch (\Exception $e) {
            // 如果没有定义 login 路由，返回 401 错误
            return response()->json([
                'message' => '未认证，请先登录',
                'error' => 'Unauthenticated'
            ], 401);
        }
    }
}
