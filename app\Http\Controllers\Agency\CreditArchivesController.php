<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use App\Models\CreditArchive;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Agency-CreditArchives",
 *     description="企业端信用档案查看"
 * )
 */
class CreditArchivesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/agency/credit-archives",
     *     summary="企业及经纪人信用档案列表",
     *     tags={"Agency-CreditArchives"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index(Request $request)
    {
        $agency = $request->user()->agency;
        if (!$agency) {
            return response()->json(['message' => '企业信息不存在'], 404);
        }
        $archives = CreditArchive::where('entity_type', 'agency')
            ->where('entity_id', $agency->id)
            ->orWhere('entity_type', 'broker')
            ->whereIn('entity_id', function ($query) use ($agency) {
                $query->select('id')->from('brokers')->where('agency_id', $agency->id);
            })
            ->paginate(20);
        return response()->json($archives);
    }
}
