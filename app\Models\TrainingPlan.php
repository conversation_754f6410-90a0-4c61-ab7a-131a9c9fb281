<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingPlan extends Model
{
    use HasFactory;

    protected $fillable = ['title', 'price', 'deadline'];

    protected $casts = [
        'deadline' => 'datetime',
    ];

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'training_plan_courses');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }
}
