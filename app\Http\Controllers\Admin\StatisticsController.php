<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Broker;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Admin-Statistics",
 *     description="数据统计"
 * )
 */
class StatisticsController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/statistics",
     *     summary="统计数据",
     *     tags={"Admin-Statistics"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index()
    {
        $agencyCount = Agency::count();
        $brokerCount = Broker::count();
        $brokerStatusCount = Broker::select('status', \DB::raw('count(*) as total'))->groupBy('status')->get();
        $brokerAgencyCount = Broker::select('agency_id', \DB::raw('count(*) as total'))->groupBy('agency_id')->get();
        return response()->json([
            'agency_count' => $agencyCount,
            'broker_count' => $brokerCount,
            'broker_status_count' => $brokerStatusCount,
            'broker_agency_count' => $brokerAgencyCount
        ]);
    }
}
