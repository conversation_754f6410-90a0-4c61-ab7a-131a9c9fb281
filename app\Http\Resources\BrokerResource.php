<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BrokerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'agency_id' => $this->agency_id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'id_card' => $this->id_card,
            'id_card_image' => $this->id_card_image,
            'phone' => $this->phone,
            'certificate_type' => $this->certificate_type,
            'certificate_number' => $this->certificate_number,
            'certificate_image' => $this->certificate_image,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
