<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CaptchaController;
use App\Http\Controllers\Agency\AgencyAuthController;
use App\Http\Controllers\Agency\ProfileController;
use App\Http\Controllers\Agency\BrokersController;
use App\Http\Controllers\Agency\CreditArchivesController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/login', [AuthController::class, 'login']);

// 验证码接口
Route::get('/captcha', [CaptchaController::class, 'generate']);
Route::post('/captcha/verify', [CaptchaController::class, 'verify']);

Route::post('/agency/register', [AgencyAuthController::class, 'register']);

// 机构端相关接口
Route::middleware(['auth:sanctum', 'role:agency_admin'])->prefix('agency')->group(function () {
    // 企业信息管理（查看/编辑/上传）不加审核中间件，允许未审核和驳回时编辑
    Route::get('/profile', [ProfileController::class, 'show']);
    Route::put('/profile', [ProfileController::class, 'update']);
    Route::post('/profile/upload', [ProfileController::class, 'upload']);
    Route::get('/info', [ProfileController::class, 'info']);

    // 其它操作需审核通过
    Route::middleware(['check.agency.profile'])->group(function () {
        // 经纪人管理
        Route::get('/brokers', [BrokersController::class, 'index']);
        Route::post('/brokers', [BrokersController::class, 'store']);
        Route::put('/brokers/{id}', [BrokersController::class, 'update']);
        Route::delete('/brokers/{id}', [BrokersController::class, 'destroy']);

        // 信用档案查看
        Route::get('/credit-archives', [CreditArchivesController::class, 'index']);
    });
});

// 通用文件上传接口
Route::middleware('auth:sanctum')->post('/upload', [App\Http\Controllers\FileController::class, 'upload']);
