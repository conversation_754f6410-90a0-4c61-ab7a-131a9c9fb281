<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseVideo;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Admin-Courses",
 *     description="课程管理"
 * )
 */
class CoursesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/admin/courses",
     *     summary="课程列表",
     *     tags={"Admin-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function index()
    {
        $courses = Course::paginate(20);
        return response()->json($courses);
    }

    /**
     * @OA\Post(
     *     path="/admin/courses",
     *     summary="创建课程",
     *     tags={"Admin-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="description", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function store(Request $request)
    {
        $course = Course::create($request->all());
        return response()->json(['message' => '创建成功', 'course' => $course]);
    }

    /**
     * @OA\Get(
     *     path="/admin/courses/{id}",
     *     summary="课程详情",
     *     tags={"Admin-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="课程ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function show($id)
    {
        $course = Course::findOrFail($id);
        return response()->json($course);
    }

    /**
     * @OA\Put(
     *     path="/admin/courses/{id}",
     *     summary="编辑课程",
     *     tags={"Admin-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="课程ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="description", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function update(Request $request, $id)
    {
        $course = Course::findOrFail($id);
        $course->update($request->all());
        return response()->json(['message' => '更新成功']);
    }

    /**
     * @OA\Delete(
     *     path="/admin/courses/{id}",
     *     summary="删除课程",
     *     tags={"Admin-Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="课程ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="成功")
     * )
     */
    public function destroy($id)
    {
        $course = Course::findOrFail($id);
        $course->delete();
        return response()->json(['message' => '删除成功']);
    }

    public function videos(Request $request, $courseId)
    {
        if ($request->isMethod('get')) {
            $videos = CourseVideo::where('course_id', $courseId)->paginate(20);
            return response()->json($videos);
        } elseif ($request->isMethod('post')) {
            $video = CourseVideo::create(array_merge($request->all(), ['course_id' => $courseId]));
            return response()->json(['message' => '创建成功', 'video' => $video]);
        }
        return response()->json(['message' => '无效的请求方法'], 400);
    }

    public function pricing(Request $request, $id)
    {
        $course = Course::findOrFail($id);
        $price = $request->input('price');
        if (is_numeric($price) && $price >= 0) {
            $course->price = $price;
            $course->save();
            return response()->json(['message' => '定价成功']);
        }
        return response()->json(['message' => '无效的价格'], 400);
    }
}
