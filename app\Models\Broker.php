<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\SerializeDate;

class Broker extends BaseModel
{
    use HasFactory, SoftDeletes, SerializeDate;

    protected $fillable = [
        'agency_id', 'user_id', 'name', 'id_card', 'id_card_image',
        'phone', 'certificate_type', 'certificate_number', 'certificate_image',
        'status', 'initial_password', 'reject_reason'
    ];

    protected $casts = [
        'certificate_type' => 'string',
        'status' => 'string',
    ];

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function histories()
    {
        return $this->hasMany(BrokerHistory::class);
    }

    public function creditArchives()
    {
        return $this->morphMany(CreditArchive::class, 'entity');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function learningProgress()
    {
        return $this->hasMany(LearningProgress::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }
}
